resource "grafana_rule_group" "driver_account_api" {
  name               = "Driver Account API - 12h"
  folder_uid         = grafana_folder.mobile_squad.uid
  interval_seconds   = 43200
  disable_provenance = true

  rule {
    name      = "Driver Account API Availability 99.9% alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.999],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 43200
        to   = 0
      }

      datasource_uid = "AthenaDestProdAccLog"
      model          = "{\"connectionArgs\":{\"catalog\":\"__default\",\"database\":\"__default\",\"region\":\"__default\"},\"datasource\":{\"uid\":\"AthenaDestProdAccLog\"},\"format\":0,\"intervalMs\":30000,\"maxDataPoints\":1500,\"rawSQL\":\"select x.time,\\ncast(x.success as double) / cast(y.total as double) as Availability\\nfrom (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as success\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  and elb_status_code \\u003c 500\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) x\\njoin (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as total\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) y\\non x.time=y.time\\norder by x.time, y.time\",\"refId\":\"B\",\"table\":\"driver_account_api_load_balancer\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "25"
      __dashboardUid__ = grafana_dashboard.mobile_squad_driver_account_api.uid
      __panelId__      = "9"
      message          = "availability is less than 99.9% for our users"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-ev-driver"
      og_priority = "P1"
    }
    is_paused = false
  }

  rule {
    name      = "Driver Account API Latency < 500ms 99.9% alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.999],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 2592000
        to   = 0
      }

      datasource_uid = "AthenaDestProdAccLog"
      model          = "{\"connectionArgs\":{\"catalog\":\"__default\",\"database\":\"__default\",\"region\":\"__default\"},\"datasource\":{\"uid\":\"AthenaDestProdAccLog\"},\"format\":0,\"intervalMs\":1800000,\"maxDataPoints\":1500,\"rawSQL\":\"select x.time,\\ncast(x.success as double) / cast(y.total as double) as Availability\\nfrom (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as success\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  and target_processing_time \\u003c (500.0 / 1000.0)\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) x\\njoin (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as total\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) y\\non x.time = y.time\\norder by x.time, y.time\",\"refId\":\"B\",\"table\":\"driver_account_api_load_balancer\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "27"
      __dashboardUid__ = grafana_dashboard.mobile_squad_driver_account_api.uid
      __panelId__      = "11"
      message          = "latency is greater than 500ms for more than 0.1% of our users"
    }

    labels = {
      Environment = "Production"
      squad       = "experience-ev-driver"
      og_priority = "P1"
    }

    is_paused = false
  }
}

resource "grafana_rule_group" "driver_account_web_app" {
  name               = "Driver Account Web App - 12h"
  folder_uid         = grafana_folder.mobile_squad.uid
  interval_seconds   = 43200
  disable_provenance = true

  rule {
    name      = "Driver Account Webapp Availability 99.9% alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.999],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 43200
        to   = 0
      }

      datasource_uid = "AthenaDestProdAccLog"
      model          = "{\"connectionArgs\":{\"catalog\":\"__default\",\"database\":\"__default\",\"region\":\"__default\"},\"datasource\":{\"uid\":\"AthenaDestProdAccLog\"},\"format\":0,\"intervalMs\":30000,\"maxDataPoints\":1500,\"rawSQL\":\"select x.time,\\ncast(x.success as double) / cast(y.total as double) as Availability\\nfrom (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as success\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  and elb_status_code \\u003c 500\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) x\\njoin (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as total\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) y\\non x.time=y.time\\norder by x.time, y.time\",\"refId\":\"B\",\"table\":\"driver_account_api_load_balancer\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "17"
      __dashboardUid__ = grafana_dashboard.mobile_squad_driver_account_web_app.uid
      __panelId__      = "9"
      message          = "availability is less than 99.9% for our users"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-ev-driver"
      og_priority = "P1"
    }
    is_paused = false
  }

  rule {
    name      = "Driver Account Webapp Latency < 500ms 99.9% alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.999],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 43200
        to   = 0
      }

      datasource_uid = "AthenaDestProdAccLog"
      model          = "{\"connectionArgs\":{\"catalog\":\"__default\",\"database\":\"__default\",\"region\":\"__default\"},\"datasource\":{\"uid\":\"AthenaDestProdAccLog\"},\"format\":0,\"intervalMs\":30000,\"maxDataPoints\":1500,\"rawSQL\":\"select x.time,\\ncast(x.success as double) / cast(y.total as double) as Availability\\nfrom (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as success\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  and target_processing_time \\u003c (500.0 / 1000.0)\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) x\\njoin (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as total\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) y\\non x.time = y.time\\norder by x.time, y.time\",\"refId\":\"B\",\"table\":\"driver_account_api_load_balancer\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "18"
      __dashboardUid__ = grafana_dashboard.mobile_squad_driver_account_web_app.uid
      __panelId__      = "11"
      message          = "latency is greater than 500ms for more than 0.1% of our users"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-ev-driver"
      og_priority = "P1"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "installer_api" {
  name               = "Installer API - 12h"
  folder_uid         = grafana_folder.mobile_squad.uid
  interval_seconds   = 43200
  disable_provenance = true

  rule {
    name      = "Installer API Availability 99.9% alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.999],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 2592000
        to   = 0
      }

      datasource_uid = "AthenaDestProdAccLog"
      model          = "{\"connectionArgs\":{\"catalog\":\"__default\",\"database\":\"__default\",\"region\":\"__default\"},\"datasource\":{\"uid\":\"AthenaDestProdAccLog\"},\"format\":0,\"intervalMs\":1800000,\"maxDataPoints\":1500,\"rawSQL\":\"select x.time,\\ncast(x.success as double) / cast(y.total as double) as Availability\\nfrom (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as success\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  and elb_status_code \\u003c 500\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) x\\njoin (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as total\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) y\\non x.time=y.time\\norder by x.time, y.time\",\"refId\":\"B\",\"table\":\"installer_api_load_balancer\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "19"
      __dashboardUid__ = grafana_dashboard.mobile_squad_installer_api.uid
      __panelId__      = "9"
      message          = "availability is less than 99.9% for our users"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-ev-driver"
      og_priority = "P1"
    }
    is_paused = false
  }

  rule {
    name      = "Installer API Latency SLO breached alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.999],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 2592000
        to   = 0
      }

      datasource_uid = "AthenaDestProdAccLog"
      model          = "{\"connectionArgs\":{\"catalog\":\"__default\",\"database\":\"__default\",\"region\":\"__default\"},\"datasource\":{\"uid\":\"AthenaDestProdAccLog\"},\"format\":0,\"intervalMs\":1800000,\"maxDataPoints\":1500,\"rawSQL\":\"select x.time,\\ncast(x.success as double) / cast(y.total as double) as Availability\\nfrom (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as success\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  and target_processing_time \\u003c (500.0 / 1000.0)\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) x\\njoin (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as total\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) y\\non x.time = y.time\\norder by x.time, y.time\",\"refId\":\"B\",\"table\":\"installer_api_load_balancer\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "20"
      __dashboardUid__ = grafana_dashboard.mobile_squad_installer_api.uid
      __panelId__      = "11"
      message          = "latency is greater than 500ms for more than 0.1% of our users"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-ev-driver"
      og_priority = "P1"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "mobile_api" {
  name               = "Mobile API - 12h"
  folder_uid         = grafana_folder.mobile_squad.uid
  interval_seconds   = 43200
  disable_provenance = true

  rule {
    name      = "Mobile API Availability breached alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.999],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 2592000
        to   = 0
      }

      datasource_uid = "AthenaDestProdAccLog"
      model          = "{\"connectionArgs\":{\"catalog\":\"__default\",\"database\":\"__default\",\"region\":\"__default\"},\"datasource\":{\"uid\":\"AthenaDestProdAccLog\"},\"format\":0,\"intervalMs\":1800000,\"maxDataPoints\":1500,\"rawSQL\":\"select x.time,\\ncast(x.success as double) / cast(y.total as double) as Availability\\nfrom (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as success\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  and elb_status_code \\u003c 500\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) x\\njoin (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as total\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) y\\non x.time=y.time\\norder by x.time, y.time\",\"refId\":\"B\",\"table\":\"mobile_api_load_balancer\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "26"
      __dashboardUid__ = grafana_dashboard.mobile_squad_api.uid
      __panelId__      = "9"
      message          = "availability is less than 99.9% for our users"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-ev-driver"
      og_priority = "P1"
    }
    is_paused = false
  }

  rule {
    name      = "Mobile API Latency SLO breached alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.999],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 2592000
        to   = 0
      }

      datasource_uid = "AthenaDestProdAccLog"
      model          = "{\"connectionArgs\":{\"catalog\":\"__default\",\"database\":\"__default\",\"region\":\"__default\"},\"datasource\":{\"uid\":\"AthenaDestProdAccLog\"},\"format\":0,\"intervalMs\":1800000,\"maxDataPoints\":1500,\"rawSQL\":\"select x.time,\\ncast(x.success as double) / cast(y.total as double) as Availability\\nfrom (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as success\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  and target_processing_time \\u003c (500.0 / 1000.0)\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) x\\njoin (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as total\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) y\\non x.time = y.time\\norder by x.time, y.time\",\"refId\":\"B\",\"table\":\"mobile_api_load_balancer\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "28"
      __dashboardUid__ = grafana_dashboard.mobile_squad_api.uid
      __panelId__      = "11"
      message          = "latency is greater than 500ms for more than 0.1% of our users"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-ev-driver"
      og_priority = "P1"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "opencharge_web_app" {
  name               = "Opencharge Web App - 12h"
  folder_uid         = grafana_folder.mobile_squad.uid
  interval_seconds   = 43200
  disable_provenance = true

  rule {
    name      = "Opencharge Web App Availability breached alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.999],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 2592000
        to   = 0
      }

      datasource_uid = "AthenaPodPoint"
      model          = "{\"connectionArgs\":{\"catalog\":\"__default\",\"database\":\"opencharge_web_app_prod\",\"region\":\"__default\"},\"datasource\":{\"uid\":\"AthenaPodPoint\"},\"format\":0,\"intervalMs\":1800000,\"maxDataPoints\":1500,\"rawSQL\":\"select x.time,\\ncast(x.success as double) / cast(y.total as double) as Availability\\nfrom (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as success\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  and elb_status_code \\u003c 500\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) x\\njoin (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as total\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) y\\non x.time=y.time\\norder by x.time, y.time\",\"refId\":\"B\",\"table\":\"alb_access_logs\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "15"
      __dashboardUid__ = grafana_dashboard.mobile_squad_opencharge_web_app.uid
      __panelId__      = "9"
      message          = "availability is less than 99.9% for our users"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-ev-driver"
      og_priority = "P1"
    }
    is_paused = false
  }

  rule {
    name      = "Opencharge Web App Latency SLO breach alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.999],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 2592000
        to   = 0
      }

      datasource_uid = "AthenaPodPoint"
      model          = "{\"connectionArgs\":{\"catalog\":\"__default\",\"database\":\"opencharge_web_app_prod\",\"region\":\"__default\"},\"datasource\":{\"type\":\"grafana-athena-datasource\",\"uid\":\"AthenaPodPoint\"},\"format\":0,\"intervalMs\":1800000,\"maxDataPoints\":1500,\"rawSQL\":\"select x.time,\\ncast(x.success as double) / cast(y.total as double) as Availability\\nfrom (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as success\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  and target_processing_time \\u003c (15000.0 / 1000.0)\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) x\\njoin (\\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\\n  count(*) as total\\n  from $__table\\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\\n  order by time\\n) y\\non x.time = y.time\\norder by x.time, y.time\",\"refId\":\"B\",\"table\":\"alb_access_logs\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "16"
      __dashboardUid__ = grafana_dashboard.mobile_squad_opencharge_web_app.uid
      __panelId__      = "11"
      message          = "latency is greater than 15s for more than 0.1% of our users"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-ev-driver"
      og_priority = "P1"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "charge_optimisation_api" {
  org_id           = 1
  name             = "Flex Provider API - 1m"
  folder_uid       = "fldr_network_domain"
  interval_seconds = 60

  rule {
    name      = "Flex Provider API Errors alert"
    condition = "A"

    data {
      ref_id = "B"

      relative_time_range {
        from = 60
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"5xx Errors\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"ApiName\":\"prod-flex-provider-api\",\"Stage\":\"prod\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":50,\"label\":\"5xx Errors\",\"logGroups\":[],\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"5XXError\",\"metricQueryType\":0,\"namespace\":\"AWS/ApiGateway\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"B\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "A"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "OK"
    exec_err_state = "Alerting"
    annotations = {
      __alertId__      = "6"
      __dashboardUid__ = "lbrs-TFSz"
      __panelId__      = "4"
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
  rule {
    name      = "Flex Provider API - Lambda Errors alert"
    condition = "H"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-api-prod-get-invitation\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-api-prod-patch-flex-dispatch\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"B\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "C"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-api-prod-post-flex-dispatch\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"C\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "D"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-api-prod-post-invitation\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"D\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "E"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-notifier-prod-schedule-flex-request-execution\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"E\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "F"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-notifier-prod-stream-flex-status\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"F\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "G"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-notifier-prod-update-flex-request-status\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"G\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "H"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"C\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"D\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"E\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"F\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"G\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"H\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "KeepLast"
    exec_err_state = "Alerting"
    annotations = {
      __alertId__      = "7"
      __dashboardUid__ = "lbrs-TFSz"
      __panelId__      = "11"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "enode_api" {
  name               = "Enode API - 1m"
  folder_uid         = grafana_folder.network_domain.uid
  interval_seconds   = 60
  disable_provenance = true

  rule {
    name      = "Enode API Requests / Errors alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"max\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"5XX Errors\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"ApiName\":\"prod-enode-webhook-api\",\"Stage\":\"prod\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"5XXError\",\"metricQueryType\":0,\"namespace\":\"AWS/ApiGateway\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"B\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }

    no_data_state  = "KeepLast"
    exec_err_state = "Alerting"
    for            = "2m"
    annotations = {
      __alertId__      = "4"
      __dashboardUid__ = grafana_dashboard.nd_enode_api.uid
      __panelId__      = "2"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }

  rule {
    name      = "Enode API Latency alert"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"Max Latency\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"ApiName\":\"prod-enode-webhook-api\",\"Stage\":\"prod\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Latency\",\"metricQueryType\":0,\"namespace\":\"AWS/ApiGateway\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Maximum\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[6000],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"max\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "KeepLast"
    exec_err_state = "Alerting"
    for            = "2m"
    annotations = {
      __alertId__      = "5"
      __dashboardUid__ = grafana_dashboard.nd_enode_api.uid
      __panelId__      = "3"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "flex_provider_api" {
  org_id           = 1
  name             = "Flex Provider API - 1m"
  folder_uid       = "fldr_network_domain"
  interval_seconds = 60

  rule {
    name      = "Flex Provider API Errors alert"
    condition = "A"

    data {
      ref_id = "B"

      relative_time_range {
        from = 60
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"5xx Errors\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"ApiName\":\"prod-flex-provider-api\",\"Stage\":\"prod\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":50,\"label\":\"5xx Errors\",\"logGroups\":[],\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"5XXError\",\"metricQueryType\":0,\"namespace\":\"AWS/ApiGateway\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"B\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "A"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "OK"
    exec_err_state = "Alerting"
    annotations = {
      __alertId__      = "6"
      __dashboardUid__ = "lbrs-TFSz"
      __panelId__      = "4"
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
  rule {
    name      = "Flex Provider API - Lambda Errors alert"
    condition = "H"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-api-prod-get-invitation\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-api-prod-patch-flex-dispatch\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"B\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "C"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-api-prod-post-flex-dispatch\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"C\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "D"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-api-prod-post-invitation\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"D\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "E"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-notifier-prod-schedule-flex-request-execution\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"E\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "F"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-notifier-prod-stream-flex-status\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"F\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "G"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"{{FunctionName}}\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"FunctionName\":\"flex-provider-notifier-prod-update-flex-request-status\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"Errors\",\"metricQueryType\":0,\"namespace\":\"AWS/Lambda\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"G\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "H"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"C\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"D\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"E\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"F\"]},\"reducer\":{\"type\":\"avg\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"G\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"H\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "KeepLast"
    exec_err_state = "Alerting"
    annotations = {
      __alertId__      = "7"
      __dashboardUid__ = "lbrs-TFSz"
      __panelId__      = "11"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "smart_charging_service_api" {
  org_id           = 1
  name             = "Smart Charging Service API - 1m"
  folder_uid       = "fldr_network_domain"
  interval_seconds = 60

  rule {
    name      = "Smart Charging Service API - Response Alert"
    condition = "A"

    data {
      ref_id = "4xxELB"

      relative_time_range {
        from = 60
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"ELB 4xx Responses\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"LoadBalancer\":\"app/smart-charging-service-api-alb/c9fea0eed6b12d55\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":50,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"HTTPCode_ELB_4XX_Count\",\"metricQueryType\":0,\"namespace\":\"AWS/ApplicationELB\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"4xxELB\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "4xxHTTP"

      relative_time_range {
        from = 60
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"HTTP 4xx Responses\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"LoadBalancer\":\"app/smart-charging-service-api-alb/c9fea0eed6b12d55\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":50,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"HTTPCode_Target_4XX_Count\",\"metricQueryType\":0,\"namespace\":\"AWS/ApplicationELB\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"4xxHTTP\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "5xxELB"

      relative_time_range {
        from = 60
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"ELB 5xx Responses\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"LoadBalancer\":\"app/smart-charging-service-api-alb/c9fea0eed6b12d55\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":50,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"HTTPCode_ELB_5XX_Count\",\"metricQueryType\":0,\"namespace\":\"AWS/ApplicationELB\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"5xxELB\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "5xxHTTP"

      relative_time_range {
        from = 60
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"HTTP 5xx Responses\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"LoadBalancer\":\"app/smart-charging-service-api-alb/c9fea0eed6b12d55\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":50,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"HTTPCode_Target_5XX_Count\",\"metricQueryType\":0,\"namespace\":\"AWS/ApplicationELB\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"5xxHTTP\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Sum\"}"
    }
    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"5xxELB\"]},\"reducer\":{\"type\":\"max\"}},{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"5xxHTTP\"]},\"reducer\":{\"type\":\"max\"}},{\"evaluator\":{\"params\":[50],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"4xxELB\"]},\"reducer\":{\"type\":\"max\"}},{\"evaluator\":{\"params\":[50],\"type\":\"gt\"},\"operator\":{\"type\":\"or\"},\"query\":{\"params\":[\"4xxHTTP\"]},\"reducer\":{\"type\":\"max\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "OK"
    exec_err_state = "Alerting"
    annotations = {
      __alertId__      = "8"
      __dashboardUid__ = "7uNBEb-Vz"
      __panelId__      = "21"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
  rule {
    name      = "Cache Size alert"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 60
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"CacheClusterId\":\"*\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":50,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"DatabaseMemoryUsagePercentage\",\"metricQueryType\":0,\"namespace\":\"AWS/ElastiCache\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[80],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    annotations = {
      __alertId__      = "9"
      __dashboardUid__ = "7uNBEb-Vz"
      __panelId__      = "26"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
  rule {
    name      = "Smart Charging API - Task Count alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[4],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"RunningTaskCount\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }
    data {
      ref_id = "RunningTaskCount"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"Running Task Count\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"ClusterName\":\"smart-charging-service\",\"ServiceName\":\"smart-charging-service-api\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"label\":\"Running Task Count\",\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"RunningTaskCount\",\"metricQueryType\":0,\"namespace\":\"ECS/ContainerInsights\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"RunningTaskCount\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Maximum\"}"
    }

    no_data_state  = "KeepLast"
    exec_err_state = "Alerting"
    for            = "2m"
    annotations = {
      __alertId__      = "10"
      __dashboardUid__ = "7uNBEb-Vz"
      __panelId__      = "2"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
  rule {
    name      = "Smart Charging API - CPU Alert"
    condition = "A"

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[60],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"CPUAvg\"]},\"reducer\":{\"type\":\"max\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }
    data {
      ref_id = "CPUAvg"

      relative_time_range {
        from = 120
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"Average\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"ClusterName\":\"smart-charging-service\",\"ServiceName\":\"smart-charging-service-api\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":100,\"label\":\"Average\",\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"CPUUtilization\",\"metricQueryType\":0,\"namespace\":\"AWS/ECS\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"CPUAvg\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }

    no_data_state  = "KeepLast"
    exec_err_state = "Alerting"
    annotations = {
      __alertId__      = "11"
      __dashboardUid__ = "7uNBEb-Vz"
      __panelId__      = "4"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
  rule {
    name      = "Smart Charging Service API - Memory Alert"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 120
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"Average\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"ClusterName\":\"smart-charging-service\",\"ServiceName\":\"smart-charging-service-api\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":100,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"MemoryUtilization\",\"metricQueryType\":0,\"namespace\":\"AWS/ECS\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[50],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "KeepLast"
    exec_err_state = "Alerting"
    for            = "2m"
    annotations = {
      __alertId__      = "12"
      __dashboardUid__ = "7uNBEb-Vz"
      __panelId__      = "6"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
  rule {
    name      = "ChargingProfiles Rejected Alert"
    condition = "C"

    data {
      ref_id = "A"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{},\"expression\":\"fields @timestamp, @message, @logStream, @log\\n| filter @message like /\\\"responseType\\\":\\\"RES\\\"/ and @message like /\\\"status\\\\\\\\\\\":\\\\\\\\\\\"Rejected\\\\\\\\\\\"/\\n| stats count(*) as count by bin(5m)\\n| sort @timestamp desc\",\"id\":\"\",\"intervalMs\":1000,\"label\":\"\",\"logGroups\":[{\"accountId\":\"************\",\"arn\":\"arn:aws:logs:eu-west-1:************:log-group:/ecs/smart-charging-service-api:*\",\"name\":\"/ecs/smart-charging-service-api\"}],\"matchExact\":true,\"maxDataPoints\":43200,\"metricEditorMode\":0,\"metricName\":\"\",\"metricQueryType\":0,\"namespace\":\"\",\"period\":\"\",\"queryMode\":\"Logs\",\"refId\":\"A\",\"region\":\"eu-west-1\",\"sqlExpression\":\"\",\"statistic\":\"Average\",\"statsGroups\":[\"bin(5m)\"]}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"A\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"reducer\":\"last\",\"refId\":\"B\",\"settings\":{\"mode\":\"replaceNN\",\"replaceWithValue\":0},\"type\":\"reduce\"}"
    }
    data {
      ref_id = "C"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"B\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"C\",\"type\":\"threshold\"}"
    }

    no_data_state  = "OK"
    exec_err_state = "Error"
    annotations = {
      __dashboardUid__ = "7uNBEb-Vz"
      __panelId__      = "34"
      summary          = "We have one or more charging profiles that has been rejected by a charging station."
    }
    labels = {
      squad = "network-grid"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "smart_charging_service_db" {
  name               = "Smart Charging Service DB - 1m"
  folder_uid         = grafana_folder.network_domain.uid
  interval_seconds   = 60
  disable_provenance = true

  rule {
    name      = "Smart Charging DB CPU Utilisation alert"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"DBClusterIdentifier\":\"smart-charging\",\"Role\":\"*\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"CPUUtilization\",\"metricQueryType\":0,\"namespace\":\"AWS/RDS\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[60],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "2m"
    annotations = {
      __alertId__      = "2"
      __dashboardUid__ = grafana_dashboard.nd_smart_charging_service_db.uid
      __panelId__      = "2"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
  rule {
    name      = "DB Connections alert"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwCsStateProd"
      model          = "{\"alias\":\"\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwCsStateProd\"},\"dimensions\":{\"DBClusterIdentifier\":\"smart-charging\",\"Role\":\"*\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":true,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"DatabaseConnections\",\"metricQueryType\":0,\"namespace\":\"AWS/RDS\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[350],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "KeepLast"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "3"
      __dashboardUid__ = grafana_dashboard.nd_smart_charging_service_db.uid
      __panelId__      = "6"
      message          = ""
    }
    labels = {
      Environment = "Production"
      squad       = "network-grid"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "alerts" {
  name               = "Alerts - 1m"
  folder_uid         = grafana_folder.experience.uid
  interval_seconds   = 60
  disable_provenance = true

  rule {
    name      = "container cpu"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwDestProd"
      model          = "{\"alias\":\"\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwDestProd\"},\"dimensions\":{},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":false,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"CPUUtilization\",\"metricQueryType\":0,\"namespace\":\"AWS/ECS\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[90],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "29"
      __dashboardUid__ = grafana_dashboard.experience_alerts.uid
      __panelId__      = "2"
      message          = ""
    }
    labels = {
      environment = "prod"
      squad       = "experience-commercial"
    }
    is_paused = false
  }

  rule {
    name      = "container memory"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwDestProd"
      model          = "{\"alias\":\"\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwDestProd\"},\"dimensions\":{},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":false,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"MemoryUtilization\",\"metricQueryType\":0,\"namespace\":\"AWS/ECS\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[90],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "30"
      __dashboardUid__ = grafana_dashboard.experience_alerts.uid
      __panelId__      = "6"
      message          = ""
    }
    labels = {
      environment = "prod"
      squad       = "experience-commercial"
    }
    is_paused = false
  }

  rule {
    name      = "running task count"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwDestProd"
      model          = "{\"alias\":\"\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwDestProd\"},\"dimensions\":{\"ClusterName\":\"destination\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":false,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"DesiredTaskCount\",\"metricQueryType\":0,\"namespace\":\"ECS/ContainerInsights\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[1],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "31"
      __dashboardUid__ = grafana_dashboard.experience_alerts.uid
      __panelId__      = "8"
      message          = ""
    }
    labels = {
      squad       = "experience-commercial"
      environment = "prod"
    }
    is_paused = false
  }

  rule {
    name      = "pending task count"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwDestProd"
      model          = "{\"alias\":\"\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwDestProd\"},\"dimensions\":{\"ClusterName\":\"destination\"},\"expression\":\"\",\"id\":\"\",\"intervalMs\":200,\"matchExact\":false,\"maxDataPoints\":1500,\"metricEditorMode\":0,\"metricName\":\"PendingTaskCount\",\"metricQueryType\":0,\"namespace\":\"ECS/ContainerInsights\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"A\",\"region\":\"default\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[1],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "33"
      __dashboardUid__ = grafana_dashboard.experience_alerts.uid
      __panelId__      = "12"
      message          = ""
    }
    labels = {
      squad       = "experience-commercial"
      environment = "prod"
    }
    is_paused = false
  }

  rule {
    name      = "firmware service integration errors"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwDestProd"
      model          = "{\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwDestProd\"},\"expression\":\"stats count() by bin(1m)\\n| sort @timestamp desc\\n| filter ispresent(msg)\\n| filter msg like /error finding pod firmware/\",\"id\":\"\",\"intervalMs\":200,\"logGroupNames\":[\"/ecs/destination-site-admin-api\",\"/ecs/internal-site-admin-api\"],\"maxDataPoints\":1500,\"namespace\":\"\",\"queryMode\":\"Logs\",\"refId\":\"A\",\"region\":\"default\",\"statsGroups\":[\"bin(1m)\"]}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[1],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "OK"
    exec_err_state = "KeepLast"
    for            = "1m"
    annotations = {
      __alertId__      = "34"
      __dashboardUid__ = grafana_dashboard.experience_alerts.uid
      __panelId__      = "15"
      message          = ""
    }
    labels = {
      squad       = "experience-commercial"
      environment = "prod"
    }
    is_paused = false
  }

  rule {
    name      = "diagnostic service integration errors"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwDestProd"
      model          = "{\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwDestProd\"},\"expression\":\"stats count() by bin(1m)\\n| sort @timestamp desc\\n| filter ispresent(msg)\\n| filter msg like /error finding pod security events/\",\"id\":\"\",\"intervalMs\":200,\"logGroupNames\":[\"/ecs/destination-site-admin-api\",\"/ecs/internal-site-admin-api\"],\"maxDataPoints\":1500,\"namespace\":\"\",\"queryMode\":\"Logs\",\"refId\":\"A\",\"region\":\"default\",\"statsGroups\":[\"bin(1m)\"]}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[1],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "OK"
    exec_err_state = "KeepLast"
    for            = "1m"
    annotations = {
      __alertId__      = "35"
      __dashboardUid__ = grafana_dashboard.experience_alerts.uid
      __panelId__      = "17"
      message          = ""
    }
    labels = {
      squad       = "experience-commercial"
      environment = "prod"
    }
    is_paused = false
  }

  rule {
    name      = "claim charge errors (rfid)"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = "CwPodPoint"
      model          = "{\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwPodPoint\"},\"expression\":\"stats count() by bin(1m), messageId\\n| sort @timestamp desc\\n| filter messageId = 'Claim charge failed'\\n| filter requestException = 'Request failed with status code 412'\",\"id\":\"\",\"intervalMs\":200,\"logGroupNames\":[\"/aws/lambda/PODPointAPI-prod-pcbRfidChargeAuthResponse\"],\"maxDataPoints\":1500,\"namespace\":\"\",\"queryMode\":\"Logs\",\"refId\":\"A\",\"region\":\"default\",\"statsGroups\":[\"bin(1m)\",\"messageId\"]}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"type\":\"avg\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "OK"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __alertId__      = "36"
      __dashboardUid__ = grafana_dashboard.experience_alerts.uid
      __panelId__      = "19"
      message          = "A failed attempt to claim a charge has occurred using an RFID card which does not exist as an authoriser in the podadmin database."
    }
    labels = {
      squad = "experience-commercial"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "auth_attempts" {
  name               = "Auth Attempts"
  folder_uid         = grafana_folder.experience.uid
  interval_seconds   = 60
  disable_provenance = true

  rule {
    name      = "Auth Attempts"
    condition = "A"

    data {
      ref_id = "E"

      relative_time_range {
        from = 21600
        to   = 0
      }

      datasource_uid = "OsAppLogs"
      model          = "{\"alias\":\"Auth attempts (alarm)\",\"bucketAggs\":[{\"field\":\"timestamp\",\"id\":\"2\",\"settings\":{\"interval\":\"auto\"},\"type\":\"date_histogram\"}],\"datasource\":{\"type\":\"elasticsearch\",\"uid\":\"OsAppLogs\"},\"intervalMs\":1000,\"maxDataPoints\":43200,\"metrics\":[{\"id\":\"1\",\"type\":\"count\"}],\"query\":\"project:\\\"Pod Point API v3\\\" AND env:\\\"production\\\" AND message:\\\"Auth attempt\\\"\",\"refId\":\"E\",\"timeField\":\"timestamp\"}"
    }

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[200,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"E\"]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __dashboardUid__ = grafana_dashboard.experience_api3_auth_attempts.uid
      __panelId__      = "2"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-commercial"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "dms_1h" {
  name               = "DMS - 1h"
  folder_uid         = grafana_folder.experience.uid
  interval_seconds   = 3600
  disable_provenance = true

  rule {
    name      = "Prod XDP DMS Instance Storage < 5GB free"
    condition = "B"

    data {
      ref_id = "Replication Instance Free Storage"

      relative_time_range {
        from = 21600
        to   = 0
      }

      datasource_uid = "CwDestProd"
      model          = "{\"alias\":\"\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwDestProd\"},\"dimensions\":{},\"expression\":\"\",\"id\":\"\",\"intervalMs\":1000,\"label\":\"\",\"matchExact\":true,\"maxDataPoints\":43200,\"metricEditorMode\":0,\"metricName\":\"FreeStorageSpace\",\"metricQueryType\":0,\"namespace\":\"AWS/DMS\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"Replication Instance Free Storage\",\"region\":\"eu-west-1\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\"}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"Replication Instance Free Storage\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"reducer\":\"mean\",\"refId\":\"A\",\"type\":\"reduce\"}"
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[5000000000],\"type\":\"lt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\"}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"A\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"threshold\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    annotations = {
      __dashboardUid__ = grafana_dashboard.experience_dms.uid
      __panelId__      = "2"
      message          = "If storage is low, delete task logs or add more storage:\nhttps://aws.amazon.com/premiumsupport/knowledge-center/dms-replication-instance-storage-full/"
    }
    labels = {
      Environment = "Production"
      squad       = "experience-data-platform"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "dms_5m" {
  name               = "DMS - 5m"
  folder_uid         = grafana_folder.experience.uid
  interval_seconds   = 300
  disable_provenance = true
  rule {
    name      = "XDP DMS - CPU Usage Prod"
    condition = "A"

    data {
      ref_id = "CPU Avg"

      relative_time_range {
        from = 21600
        to   = 0
      }

      datasource_uid = "CwDestProd"
      model          = "{\"alias\":\"\",\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwDestProd\"},\"dimensions\":{},\"expression\":\"\",\"id\":\"\",\"intervalMs\":1000,\"label\":\"\",\"logGroups\":[],\"matchExact\":true,\"maxDataPoints\":43200,\"metricEditorMode\":0,\"metricName\":\"CPUUtilization\",\"metricQueryType\":0,\"namespace\":\"AWS/DMS\",\"period\":\"\",\"queryMode\":\"Metrics\",\"refId\":\"CPU Avg\",\"region\":\"eu-west-1\",\"sqlExpression\":\"\",\"statistic\":\"Average\"}"
    }

    data {
      ref_id = "A"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[90,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"CPU Avg\"]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"
    annotations = {
      __dashboardUid__ = grafana_dashboard.experience_dms.uid
      __panelId__      = "4"
      message          = "Avg CPU usage above 90% - consider scaling up replication instance."
    }
    labels = {
      Environment = "Production"
      squad       = "experience-data-platform"
    }
    is_paused = false
  }
}

resource "grafana_rule_group" "endpoint_performance_data_platform_api" {
  name               = "Endpoint Performance (data-platform-api) - 1h"
  folder_uid         = grafana_folder.experience.uid
  interval_seconds   = 3600
  disable_provenance = true
  rule {
    name      = "[Prod]: /sites avg response time > 3s"
    condition = "A"

    data {
      ref_id = "/sites"

      relative_time_range {
        from = 3600
        to   = 0
      }

      datasource_uid = "CwDestProd"
      model          = "{\"datasource\":{\"type\":\"cloudwatch\",\"uid\":\"CwDestProd\"},\"expression\":\"fields @timestamp, @message\\n| parse @message \\\"* * * * * * * * *\\\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\\n| filter url like /\\\"\\\\/sites/\\n| parse time_ms \\\"http.time_ms=*\\\" as sites_r_ms\",\"id\":\"\",\"intervalMs\":2000,\"logGroupNames\":[\"/ecs/data-platform-api\"],\"maxDataPoints\":1500,\"namespace\":\"\",\"queryMode\":\"Logs\",\"refId\":\"/sites\",\"region\":\"default\",\"statsGroups\":[]}"
    }

    data {
      ref_id = "A"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[3000],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"/sites\"]},\"reducer\":{\"type\":\"max\"}}],\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"A\",\"type\":\"classic_conditions\"}"
    }

    no_data_state  = "OK"
    exec_err_state = "Alerting"
    annotations = {
      __alertId__      = "32"
      __dashboardUid__ = grafana_dashboard.experience_endpoint_performance.uid
      __panelId__      = "9"
      message          = ""
    }
    labels = {
      Environment = "PRODUCTION"
      Source      = "GRAFANA_ALERTS"
      squad       = "experience-data-platform"
    }
    is_paused = false
  }
}
