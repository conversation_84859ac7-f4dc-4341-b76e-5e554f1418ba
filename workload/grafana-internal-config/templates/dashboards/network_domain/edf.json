{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 243, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}, {"color": "green", "value": 0}, {"color": "yellow", "value": 0.05}, {"color": "orange", "value": 0.08}, {"color": "red", "value": 0.1}]}, "unit": "currencyGBP"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 16, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"database": "\"edf_trading\"", "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "measure": "price_forecast", "rawQuery": "select \n  from_milliseconds(forecast_time_ms) as time,\n  price_kwh as price\nFROM \"edf_trading\".\"price_forecasts\"\norder by forecast_time_ms", "refId": "A", "table": "\"price_forecasts\""}], "timeFrom": "+36h", "title": "Day Ahead Forecast", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 12, "panels": [], "title": "Trading (Pilot)", "type": "row"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 0}]}, "unit": "kwatth"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 20, "options": {"legend": {"calcs": ["sum"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"database": "\"edf_trading\"", "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "measure": "price_forecast", "rawQuery": "select \n  from_milliseconds(traded_period_start_ms) as time,\n  traded_volume_kwh as traded_volume\nFROM \"edf_trading\".\"energy_trades\"\norder by traded_period_start_ms", "refId": "A", "table": "\"price_forecasts\""}], "timeFrom": "+36h", "title": "Energy Trades - Volume", "type": "timeseries"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 0}]}, "unit": "currencyGBP"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 17, "options": {"legend": {"calcs": ["sum"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"database": "\"edf_trading\"", "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "measure": "price_forecast", "rawQuery": "select \n  from_milliseconds(traded_period_start_ms) as time,\n  trade_cost as cost\nFROM \"edf_trading\".\"energy_trades\"\norder by traded_period_start_ms", "refId": "A", "table": "\"price_forecasts\""}], "timeFrom": "+36h", "title": "Energy Trades - Cost", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "max": 900000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 300000}, {"color": "red", "value": 600000}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"FunctionName": "edf-trading-prod-trading"}, "expression": "", "id": "", "label": "", "logGroups": [], "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "timeFrom": "7d", "title": "Trading Platform Lambda - Duration", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"FunctionName": "edf-trading-prod-trading"}, "expression": "", "id": "", "label": "", "logGroups": [], "matchExact": true, "metricEditorMode": 0, "metricName": "Errors", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "timeFrom": "7d", "title": "Trading Platform Lambda - Errors", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 25}, "id": 6, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Time"}]}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {}, "expression": "fields @timestamp, \n  energyBought.volumeMW as bought_volume, \n  energyBought.totalPrice as bought_price, \n  energySold.volumeMW as sold_volume, \n  energySold.totalPrice as sold_price, \n  totalDevices, totalDevicesFlexed\n| filter ispresent(level)\n| filter msg like /EDF Energy Trading Summary/\n| sort @timestamp desc \n| limit 50", "id": "", "label": "", "logGroups": [{"accountId": "************", "arn": "arn:aws:logs:eu-west-1:************:log-group:/aws/lambda/edf-trading-prod-trading:*", "name": "/aws/lambda/edf-trading-prod-trading"}], "matchExact": true, "metricEditorMode": 0, "metricName": "", "metricQueryType": 0, "namespace": "", "period": "", "queryMode": "Logs", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average", "statsGroups": []}], "title": "Trade Summaries", "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 34}, "id": 14, "panels": [], "title": "Energy Metrics", "type": "row"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "kwatth"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 35}, "id": 2, "options": {"legend": {"calcs": ["sum"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "measure": "", "rawQuery": "SELECT\n    time,\n    SUM(energy) AS energy\nFROM\n    energy_metrics.energy_metrics_by_30_minutes\nWHERE\n    $__timeFilter AND ppid = ANY (VALUES $edfChargers)\nGROUP BY\n    time\nORDER BY\n    time\n", "refId": "A"}], "title": "Energy Usage", "type": "timeseries"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "kwatth"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 35}, "id": 7, "options": {"legend": {"calcs": ["sum"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "measure": "", "rawQuery": "SELECT\n    time,\n    SUM(energy) AS energy\nFROM\n    energy_metrics.energy_metrics_by_30_minutes\nWHERE\n    $__timeFilter AND ppid = ANY (VALUES $edfChargers)\nGROUP BY\n    time\nORDER BY\n    time\n", "refId": "A"}], "timeShift": "28d", "title": "Energy Usage", "type": "timeseries"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 33, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "kwatth"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 44}, "id": 4, "options": {"legend": {"calcs": ["sum"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Total", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "measure": "", "rawQuery": "SELECT\n    time,\n    ppid as metric,\n    sum(energy) as energy\nFROM\n    energy_metrics.energy_metrics_by_30_minutes\nWHERE\n    $__timeFilter \n    AND ppid = ANY (VALUES $edfChargers) \n    AND energy > 0\nGROUP BY\n    time, ppid\nORDER BY\n    time, ppid\n", "refId": "A"}], "title": "Energy by Unit", "transformations": [{"id": "prepareTimeSeries", "options": {"format": "multi"}}], "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 39, "tags": [], "templating": {"list": [{"description": "", "hide": 2, "label": "EDF Chargers", "name": "edf<PERSON><PERSON><PERSON>", "query": "'PSL-103050', 'PSL-105108','PSL-105362','PSL-105863','PSL-107216','PSL-107902','PSL-111170','PSL-111529','PSL-112718','PSL-114262','PSL-114440','PSL-114626','PSL-115391','PSL-116320','PSL-116549','PSL-118367','PSL-119951','PSL-120957','PSL-121009','PSL-121112','PSL-121425','PSL-121778','PSL-122002','PSL-122017','PSL-122032','PSL-122093','PSL-122937','PSL-123781','PSL-124174','PSL-124277','PSL-126932','PSL-127079','PSL-127638','PSL-128186','PSL-128265','PSL-130014','PSL-130743','PSL-130897','PSL-131013','PSL-131587','PSL-132020','PSL-132115','PSL-132585','PSL-132607','PSL-132760','PSL-133306','PSL-133689','PSL-134151','PSL-134287','PSL-135223','PSL-135342','PSL-136252','PSL-136788','PSL-136884','PSL-136887','PSL-137601','PSL-137865','PSL-138195','PSL-138986','PSL-139520','PSL-140018','PSL-140577','PSL-141255','PSL-142039','PSL-142488','PSL-142764','PSL-143100','PSL-143421','PSL-143619','PSL-143625','PSL-144032','PSL-144118','PSL-144490','PSL-144658','PSL-145177','PSL-145218','PSL-145280','PSL-145512','PSL-146420','PSL-146557','PSL-147011','PSL-147090','PSL-147309','PSL-147426','PSL-148208','PSL-148509','PSL-148673','PSL-148879','PSL-149429','PSL-149430','PSL-149605','PSL-150102','PSL-150194','PSL-150555','PSL-150638','PSL-151008','PSL-152584','PSL-152927','PSL-153003','PSL-153354','PSL-154296','PSL-154527','PSL-154580','PSL-154992','PSL-155044','PSL-155610','PSL-155683','PSL-155702','PSL-155837','PSL-155998','PSL-156187','PSL-156484','PSL-156705','PSL-157323','PSL-157430','PSL-157679','PSL-157839','PSL-158308','PSL-158598','PSL-158633','PSL-159242','PSL-159539','PSL-160139','PSL-160510','PSL-160741','PSL-160815','PSL-160868','PSL-160886','PSL-160939','PSL-161097','PSL-161484','PSL-161580','PSL-161885','PSL-162338','PSL-162575','PSL-162600','PSL-162913','PSL-162970','PSL-164229','PSL-164699','PSL-164903','PSL-164925','PSL-165441','PSL-165507','PSL-166078','PSL-166164','PSL-166339','PSL-166553','PSL-166966','PSL-167958','PSL-167995','PSL-168309','PSL-168446','PSL-168663','PSL-168685','PSL-168850','PSL-169263','PSL-169798','PSL-170623','PSL-171153','PSL-172080','PSL-172110','PSL-173640','PSL-173776','PSL-173864','PSL-174249','PSL-174310','PSL-174348','PSL-174357','PSL-174449','PSL-174467','PSL-174553','PSL-175072','PSL-175265','PSL-175535','PSL-175884','PSL-175955','PSL-176449','PSL-176627','PSL-176951','PSL-177070','PSL-177231','PSL-177354','PSL-177737','PSL-177907','PSL-178045','PSL-178181','PSL-178346','PSL-178645','PSL-178783','PSL-179150','PSL-179678','PSL-179910','PSL-179931','PSL-179971','PSL-180062','PSL-180961','PSL-181139','PSL-181671','PSL-181759','PSL-181900','PSL-182466','PSL-183154','PSL-183731','PSL-183846','PSL-184137','PSL-184379','PSL-184586','PSL-185361','PSL-185381','PSL-185458','PSL-185723','PSL-185816','PSL-185912','PSL-186200','PSL-186226','PSL-186619','PSL-186693','PSL-187161','PSL-188109','PSL-188649','PSL-188929','PSL-189013','PSL-189275','PSL-189563','PSL-189591','PSL-190134','PSL-190145','PSL-190350','PSL-190442','PSL-190454','PSL-190493','PSL-190785','PSL-191237','PSL-191411','PSL-191730','PSL-191981','PSL-192057','PSL-193403','PSL-193411','PSL-193526','PSL-193573','PSL-193959','PSL-194928','PSL-195315','PSL-195713','PSL-196623','PSL-196995','PSL-197263','PSL-197413','PSL-197700','PSL-197746','PSL-197756','PSL-198063','PSL-198178','PSL-198334','PSL-198452','PSL-198740','PSL-198764','PSL-199014','PSL-199093','PSL-199649','PSL-199783','PSL-200269','PSL-200403','PSL-200494','PSL-200662','PSL-200876','PSL-200986','PSL-201070','PSL-201168','PSL-201186','PSL-201330','PSL-201879','PSL-201970','PSL-201988','PSL-202174','PSL-203265','PSL-203336','PSL-203551','PSL-203757','PSL-203830','PSL-203845','PSL-203964','PSL-204201','PSL-205121','PSL-206008','PSL-206053','PSL-206251','PSL-206396','PSL-206481','PSL-206628','PSL-206718','PSL-207102','PSL-207322','PSL-207678','PSL-207702','PSL-207897','PSL-208376','PSL-208387','PSL-208527','PSL-208649','PSL-209084','PSL-209553','PSL-209703','PSL-209944','PSL-211514','PSL-211876','PSL-212055','PSL-212152','PSL-212200','PSL-212328','PSL-212426','PSL-213103','PSL-213428','PSL-213876','PSL-214642','PSL-214659','PSL-214664','PSL-215187','PSL-215443','PSL-215672','PSL-215915','PSL-216159','PSL-216451','PSL-216910','PSL-217146','PSL-217173','PSL-217607','PSL-217624','PSL-218021','PSL-218069','PSL-218725','PSL-219524','PSL-219585','PSL-220349','PSL-220363','PSL-220764','PSL-220881','PSL-220911','PSL-221589','PSL-221699','PSL-221932','PSL-222013','PSL-222449','PSL-222982','PSL-223322','PSL-223623','PSL-223669','PSL-223771','PSL-224585','PSL-224704','PSL-224747','PSL-225005','PSL-225299','PSL-225694','PSL-225828','PSL-226617','PSL-227235','PSL-227441','PSL-227943','PSL-228065','PSL-228899','PSL-229131','PSL-229552','PSL-229588','PSL-230015','PSL-230052','PSL-230224','PSL-231301','PSL-231356','PSL-231377','PSL-231435','PSL-231508','PSL-231901','PSL-231994','PSL-232260','PSL-232672','PSL-232991','PSL-233183','PSL-233580','PSL-233755','PSL-234169','PSL-234331','PSL-234510','PSL-234591','PSL-234625','PSL-235699','PSL-235873','PSL-236476','PSL-237468','PSL-237936','PSL-238388','PSL-238523','PSL-238554','PSL-238620','PSL-238712','PSL-238803','PSL-238948','PSL-238970','PSL-239026','PSL-239086','PSL-239232','PSL-239796','PSL-240071','PSL-240138','PSL-241057','PSL-242238','PSL-242302','PSL-242801','PSL-242898','PSL-243529','PSL-243573','PSL-243662','PSL-243815','PSL-243927','PSL-243952','PSL-244105','PSL-244254','PSL-244709','PSL-244789','PSL-244961','PSL-245658','PSL-246497','PSL-246579','PSL-247005','PSL-247240','PSL-247286','PSL-247490','PSL-247875','PSL-248949','PSL-249393','PSL-249802','PSL-250198','PSL-250370','PSL-250673','PSL-250688','PSL-250692','PSL-251210','PSL-251330','PSL-252166','PSL-252689','PSL-253290','PSL-253372','PSL-253698','PSL-254139','PSL-254626','PSL-254830','PSL-254901','PSL-255538','PSL-255622','PSL-256297','PSL-256479','PSL-256515','PSL-256596','PSL-257077','PSL-257083','PSL-258061','PSL-259004','PSL-259088','PSL-259271','PSL-259376','PSL-259477','PSL-259729','PSL-260037','PSL-260096','PSL-260225','PSL-260456','PSL-261264','PSL-261327','PSL-262033','PSL-263032','PSL-263192','PSL-263486','PSL-263597','PSL-264392','PSL-264636','PSL-264902','PSL-265667','PSL-266188','PSL-266441','PSL-266903','PSL-267710','PSL-267723','PSL-267752','PSL-267857','PSL-268136','PSL-268196','PSL-268423','PSL-268721','PSL-268856','PSL-269053','PSL-269749','PSL-270044','PSL-270074','PSL-270407','PSL-270533','PSL-270595','PSL-271165','PSL-271487','PSL-272613','PSL-272885','PSL-272924','PSL-273031','PSL-273271','PSL-273275','PSL-273276','PSL-273304','PSL-273782','PSL-274336','PSL-274655','PSL-274852','PSL-275115','PSL-275163','PSL-275407','PSL-276989','PSL-277077','PSL-277200','PSL-277601','PSL-277728','PSL-277850','PSL-277870','PSL-278047','PSL-278512','PSL-278992','PSL-279111','PSL-279596','PSL-282411','PSL-282452','PSL-282655','PSL-285408','PSL-500131','PSL-500420','PSL-500447','PSL-500705','PSL-500710','PSL-500934','PSL-500975','PSL-500994','PSL-501292','PSL-501398','PSL-501538','PSL-501575','PSL-501967','PSL-502346','PSL-502450','PSL-502520','PSL-503261','PSL-503299','PSL-504374','PSL-504439','PSL-504595','PSL-504629','PSL-504695','PSL-504807','PSL-505218','PSL-505458','PSL-505657','PSL-505838','PSL-506550','PSL-506680','PSL-506881','PSL-507326','PSL-507759','PSL-508204','PSL-508612','PSL-509224','PSL-509235','PSL-509342','PSL-509504','PSL-510288','PSL-510384','PSL-510795','PSL-511103','PSL-511434','PSL-511477','PSL-511671','PSL-511676','PSL-511797','PSL-511826','PSL-511966','PSL-512294','PSL-512639','PSL-512836','PSL-512987','PSL-513063','PSL-513717','PSL-513795','PSL-514355','PSL-514759','PSL-514906','PSL-514914','PSL-516114','PSL-516217','PSL-516731','PSL-517461','PSL-517462','PSL-518122','PSL-518409','PSL-518604','PSL-518689','PSL-518927','PSL-519382','PSL-519750','PSL-519752','PSL-521173','PSL-521520','PSL-521579','PSL-521830','PSL-521909','PSL-521925','PSL-522190','PSL-522361','PSL-522470','PSL-522562','PSL-522585','PSL-522643','PSL-522703','PSL-522844','PSL-523473','PSL-523909','PSL-523965','PSL-524088','PSL-524106','PSL-524363','PSL-524408','PSL-524741','PSL-524962','PSL-525191','PSL-525419','PSL-525467','PSL-525615','PSL-525800','PSL-526106','PSL-526483','PSL-527381','PSL-527579','PSL-528498','PSL-528527','PSL-528539','PSL-528728','PSL-528878','PSL-528959','PSL-529625','PSL-529864','PSL-530989','PSL-532570','PSL-532593','PSL-532752','PSL-533006','PSL-533229','PSL-533255','PSL-533484','PSL-533651','PSL-534087','PSL-534328','PSL-534522','PSL-534605','PSL-534653','PSL-534714','PSL-534971','PSL-535192','PSL-535263','PSL-535576','PSL-535579','PSL-535623','PSL-535695','PSL-536473','PSL-536536','PSL-536823','PSL-537103','PSL-537261','PSL-537379','PSL-537380','PSL-537539','PSL-537540','PSL-537572','PSL-537991','PSL-538206','PSL-538236','PSL-538296','PSL-538559','PSL-539082','PSL-539172','PSL-539175','PSL-539364','PSL-539650','PSL-539662','PSL-540467','PSL-540721','PSL-540776','PSL-540893','PSL-541140','PSL-541163','PSL-541189','PSL-541722','PSL-541747','PSL-541838','PSL-541864','PSL-542359','PSL-543145','PSL-543276','PSL-543383','PSL-543866','PSL-544588','PSL-544699','PSL-544752','PSL-544785','PSL-545496','PSL-545732','PSL-545733','PSL-546061','PSL-546070','PSL-546196','PSL-546277','PSL-546400','PSL-546436','PSL-546768','PSL-546810','PSL-546883','PSL-547126','PSL-547243','PSL-547336','PSL-548575','PSL-548793','PSL-549244','PSL-549472','PSL-549496','PSL-550125','PSL-550696','PSL-552087','PSL-552460','PSL-552972','PSL-554768','PSL-554968','PSL-555654','PSL-555811','PSL-555874','PSL-555953','PSL-555982','PSL-556086','PSL-556477','PSL-556571','PSL-556888','PSL-556929','PSL-557423','PSL-557448','PSL-557733','PSL-557857','PSL-557994','PSL-558304','PSL-558533','PSL-558898','PSL-559153','PSL-559533','PSL-559583','PSL-559784','PSL-560120','PSL-560230','PSL-561056','PSL-561151','PSL-561403','PSL-561628','PSL-561860','PSL-562089','PSL-562276','PSL-562422','PSL-562461','PSL-563484','PSL-564074','PSL-564286','PSL-564619','PSL-564921','PSL-566182','PSL-566226','PSL-566256','PSL-566370','PSL-566629','PSL-566781','PSL-567150','PSL-567552','PSL-567665','PSL-567685','PSL-567707','PSL-569638','PSL-570200','PSL-570218','PSL-570321','PSL-570643','PSL-570749','PSL-571050','PSL-571533','PSL-571638','PSL-571867','PSL-572737','PSL-573466','PSL-573749','PSL-573952','PSL-574903','PSL-576211','PSL-576616','PSL-576769','PSL-577777','PSL-577964','PSL-578159','PSL-579186','PSL-579262','PSL-579519','PSL-579843','PSL-579844','PSL-580381','PSL-581515','PSL-583087','PSL-583229','PSL-584867','PSL-585590','PSL-586020','PSL-586868','PSL-587201','PSL-589161','PSL-589162','PSL-589448','PSL-589569','PSL-599628','PSL-600520','PSL-600762','PSL-601384','PSL-602626','PSL-602690','PSL-602773','PSL-602882','PSL-604600','PSL-605013','PSL-607387','PSL-617208','PSL-73238'", "skipUrlSync": false, "type": "constant"}]}, "time": {"from": "now-24h/d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "EDF Trading Dashboard: Pilot", "uid": "Ty3FGWPIk", "version": 7, "weekStart": ""}