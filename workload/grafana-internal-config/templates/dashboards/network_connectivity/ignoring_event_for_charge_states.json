{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 230, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "context.unit_id"}, "properties": [{"id": "custom.width", "value": 130}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Door"}, "properties": [{"id": "custom.width", "value": 78}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge ID"}, "properties": [{"id": "custom.width", "value": 120}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Event"}, "properties": [{"id": "custom.width", "value": 89}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Event Energy"}, "properties": [{"id": "custom.width", "value": 122}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Unit ID"}, "properties": [{"id": "custom.width", "value": 105}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Event Pilot"}, "properties": [{"id": "custom.width", "value": 111}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 0}, "id": 5, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Charge ID"}]}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "message:\"Ignoring charge state creation due to timestamps\" AND env:\"production\" AND project:\"POD Point Comms Service\"", "refId": "A", "timeField": "timestamp"}], "title": "Dropped events where energy is higher", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "context.event_energy", "operator": "-", "reducer": "sum", "right": "context.charge_state_energy"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "filterByValue", "options": {"filters": [{"config": {"id": "equal", "options": {"value": 0}}, "fieldName": "context.event_energy - context.charge_state_energy"}], "match": "all", "type": "exclude"}}, {"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.event_energy - context.charge_state_energy": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": true}, "indexByName": {"_id": 3, "_index": 4, "_type": 5, "channel": 6, "context.charge": 7, "context.charge_state_energy": 11, "context.charge_state_pilot": 13, "context.charge_state_starts_at": 9, "context.door": 2, "context.event_energy": 12, "context.event_energy - context.charge_state_energy": 23, "context.event_name": 8, "context.event_occurrence_time": 10, "context.event_pilot": 14, "context.unit_id": 1, "env": 15, "extra": 16, "highlight": 17, "host": 18, "level": 19, "message": 20, "project": 21, "sort": 22, "timestamp": 0}, "renameByName": {"context.charge": "Charge ID", "context.charge_state_energy": "Charge State Energy", "context.charge_state_pilot": "Charge State Pilot", "context.charge_state_starts_at": "Charge State Starts At", "context.door": "Door", "context.event_energy": "Event Energy", "context.event_name": "Event", "context.event_occurrence_time": "Event Occurence Time", "context.event_pilot": "Event Pilot", "context.unit_id": "Unit ID"}}}], "type": "table"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 4, "x": 0, "y": 10}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["distinctCount"], "fields": "/^context\\.unit_id$/", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "message:\"Ignoring charge state creation due to timestamps\" AND env:\"production\" AND project:\"POD Point Comms Service\"", "refId": "A", "timeField": "timestamp"}], "title": "Different Units", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 4, "x": 4, "y": 10}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["count"], "fields": "/^context\\.unit_id$/", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "message:\"Ignoring charge state creation due to timestamps\" AND env:\"production\" AND project:\"POD Point Comms Service\"", "refId": "A", "timeField": "timestamp"}], "title": "Events Ignored", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Unit ID"}, "properties": [{"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Door"}, "properties": [{"id": "custom.width", "value": 74}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Count of dropped events"}, "properties": [{"id": "custom.width", "value": 188}]}]}, "gridPos": {"h": 12, "w": 8, "x": 8, "y": 10}, "id": 4, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "message:\"Ignoring charge state creation due to timestamps\" AND env:\"production\" AND project:\"POD Point Comms Service\"", "refId": "A", "timeField": "timestamp"}], "title": "Count of occurrences per unit unit and door", "transformations": [{"id": "groupBy", "options": {"fields": {"context.charge": {"aggregations": ["count"], "operation": "aggregate"}, "context.door": {"aggregations": [], "operation": "groupby"}, "context.unit_id": {"aggregations": ["count"], "operation": "groupby"}}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "context.charge (count)"}]}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"context.charge (count)": 2, "context.door": 1, "context.unit_id": 0}, "renameByName": {"context.charge (count)": "Count of dropped events", "context.door": "Door", "context.unit_id": "Unit ID"}}}], "type": "table"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Unit ID"}, "properties": [{"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Door"}, "properties": [{"id": "custom.width", "value": 98}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Count of dropped events"}, "properties": [{"id": "custom.width", "value": 192}]}]}, "gridPos": {"h": 12, "w": 8, "x": 16, "y": 10}, "id": 6, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "message:\"Ignoring charge state creation due to timestamps\" AND env:\"production\" AND project:\"POD Point Comms Service\"", "refId": "A", "timeField": "timestamp"}], "title": "Count of occurrences per charge cycle", "transformations": [{"id": "groupBy", "options": {"fields": {"_id": {"aggregations": ["count"], "operation": "aggregate"}, "context.charge": {"aggregations": ["count"], "operation": "groupby"}, "context.door": {"aggregations": []}, "context.unit_id": {"aggregations": ["count"]}}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "_id (count)"}]}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"context.charge (count)": 2, "context.door": 1, "context.unit_id": 0}, "renameByName": {"_id (count)": "Total of ocurrences", "context.charge": "Charge ID", "context.charge (count)": "Count of dropped events", "context.door": "Door", "context.unit_id": "Unit ID"}}}], "type": "table"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Ignoring event for charge states creation due to timestamps being older than last state", "uid": "xUUDNqAIk", "version": 8, "weekStart": ""}