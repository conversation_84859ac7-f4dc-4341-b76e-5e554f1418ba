{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 84, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 31, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "NLB", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 1}, "id": 53, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"LoadBalancer": "net/ws-proxy-lb/53031e636776a45f"}, "expression": "", "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ActiveFlowCount", "metricQueryType": 0, "namespace": "AWS/NetworkELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Active Connections", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 8, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 8}, "id": 54, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"LoadBalancer": "net/ws-proxy-lb/53031e636776a45f"}, "expression": "", "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NewFlowCount", "metricQueryType": 0, "namespace": "AWS/NetworkELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "New Connections", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 15}, "id": 55, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"AvailabilityZone": "*", "LoadBalancer": "net/ws-proxy-lb/53031e636776a45f", "TargetGroup": "targetgroup/ws-proxy-blue/3e554cda454b1432"}, "expression": "", "id": "", "label": "", "matchExact": false, "metricEditorMode": 0, "metricName": "HealthyHostCount", "metricQueryType": 0, "namespace": "AWS/NetworkELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Healthy hosts per AZ (Blue)", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 15}, "id": 56, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"AvailabilityZone": "*", "LoadBalancer": "*", "TargetGroup": "targetgroup/ws-proxy-green/696203652cff16c2"}, "expression": "", "id": "", "label": "", "matchExact": false, "metricEditorMode": 0, "metricName": "HealthyHostCount", "metricQueryType": 0, "namespace": "AWS/NetworkELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Healthy hosts per AZ (Green)", "type": "stat"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 22, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Task state", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 66, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 21}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, TaskId, CpuUtilized / CpuReserved as CpuPercentage\n| filter ispresent(TaskId)\n| stats AVG(CpuPercentage) by bin(5m), TaskId", "id": "", "logGroups": [{"accountId": "************", "arn": "arn:aws:logs:eu-west-1:************:log-group:/aws/ecs/containerinsights/ws-proxy/performance:*", "name": "/aws/ecs/containerinsights/ws-proxy/performance"}], "namespace": "AWS/NetworkELB", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["bin(5m)", "TaskId"]}], "title": "CPU Utilisation by task", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 66, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 27}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, TaskId, MemoryUtilized / MemoryReserved as MemoryPercentage\n| filter ispresent(TaskId)\n| stats AVG(MemoryPercentage) by bin(5m), TaskId", "id": "", "logGroups": [{"accountId": "************", "arn": "arn:aws:logs:eu-west-1:************:log-group:/aws/ecs/containerinsights/ws-proxy/performance:*", "name": "/aws/ecs/containerinsights/ws-proxy/performance"}], "namespace": "AWS/NetworkELB", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["bin(5m)", "TaskId"]}], "title": "Memory Utilisation by task", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 33}, "id": 16, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Cluster state", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": true, "axisLabel": "CPU Utilisation", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 22, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "decimals": 1, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 34}, "id": 57, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"alias": "Minimum", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"ClusterName": "ws-proxy", "ServiceName": "ws-proxy"}, "expression": "", "id": "", "label": "Minimum", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Minimum"}, {"alias": "Maximum", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"ClusterName": "ws-proxy", "ServiceName": "ws-proxy"}, "expression": "", "hide": false, "id": "", "label": "Maximum", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Maximum"}, {"alias": "Average", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"ClusterName": "ws-proxy", "ServiceName": "ws-proxy"}, "expression": "", "hide": false, "id": "", "label": "Average", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "CPU Utilisation [Cluster]", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": true, "axisLabel": "CPU Utilisation", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 22, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "decimals": 1, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}, "id": 58, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"alias": "Minimum", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"ClusterName": "ws-proxy", "ServiceName": "ws-proxy"}, "expression": "", "id": "", "label": "Minimum", "matchExact": true, "metricEditorMode": 0, "metricName": "MemoryUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Minimum"}, {"alias": "Maximum", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"ClusterName": "ws-proxy", "ServiceName": "ws-proxy"}, "expression": "", "hide": false, "id": "", "label": "Maximum", "matchExact": true, "metricEditorMode": 0, "metricName": "MemoryUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Maximum"}, {"alias": "Average", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"ClusterName": "ws-proxy", "ServiceName": "ws-proxy"}, "expression": "", "hide": false, "id": "", "label": "Average", "matchExact": true, "metricEditorMode": 0, "metricName": "MemoryUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Memory Utilisation [Cluster]", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 48}, "id": 65, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {}, "expression": "", "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ErrorCount", "metricQueryType": 0, "namespace": "CSMS/ws-proxy", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "Errors", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 56}, "id": 25, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Certificate", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 13, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 259200}, {"color": "red", "value": 345600}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 57}, "id": 59, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "Minimum", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {}, "expression": "", "id": "", "label": "Minimum", "matchExact": true, "metricEditorMode": 0, "metricName": "CertificateExpiration", "metricQueryType": 0, "namespace": "CSMS/ws-proxy", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Minimum"}], "title": "Certificate Expiration", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-RdYlGr"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "left", "axisSoftMax": 432000, "axisSoftMin": 0, "fillOpacity": 45, "gradientMode": "scheme", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "mappings": [{"options": {"pattern": "ecs/csms-caddy/(.*)", "result": {"index": 0, "text": "$1"}}, "type": "regex"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 65}, "id": 60, "options": {"barRadius": 0, "barWidth": 0.97, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "orientation": "horizontal", "showValue": "never", "stacking": "normal", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xField": "taskId", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields expiresInSeconds, @logStream\n| filter msg = 'CERTIFICATE_DETAILS'\n| sort @timestamp desc\n| limit 1000", "id": "", "logGroups": [{"accountId": "************", "arn": "arn:aws:logs:eu-west-1:************:log-group:/ecs/ws-proxy:*", "name": "/ecs/ws-proxy"}], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Certificate Expiration By Task", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"@logStream": "taskId", "min(expiresInSeconds)": "expiresInSeconds"}}}, {"id": "groupBy", "options": {"fields": {"expiresInSeconds": {"aggregations": ["last"], "operation": "aggregate"}, "taskId": {"aggregations": [], "operation": "groupby"}}}}], "type": "barchart"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 73}, "id": 35, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "OTT", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 21, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "µs"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 74}, "id": 61, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "Average", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {}, "expression": "", "id": "", "label": "Average", "matchExact": true, "metricEditorMode": 0, "metricName": "OTTStoringLatency", "metricQueryType": 0, "namespace": "CSMS/ws-proxy", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "p95", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {}, "expression": "", "hide": false, "id": "", "label": "p95", "matchExact": true, "metricEditorMode": 0, "metricName": "OTTStoringLatency", "metricQueryType": 0, "namespace": "CSMS/ws-proxy", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "p95"}], "title": "Redis OTT Storing Latency", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "light-blue", "mode": "fixed"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "displayName": "No. of samples", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "yellow"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 83}, "id": 62, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {}, "expression": "", "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "OTTStoringLatency", "metricQueryType": 0, "namespace": "CSMS/ws-proxy", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "SampleCount"}], "title": "Redis OTT Storing Samples", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 93}, "id": 41, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Redis", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 68, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 11, "x": 0, "y": 94}, "id": 63, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"CacheClusterId": "ws-0001-001"}, "expression": "", "id": "", "label": "ws-0001-001 (AVG)", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/ElastiCache", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"CacheClusterId": "ws-0001-002"}, "expression": "", "hide": false, "id": "b", "label": "ws-0001-002 (AVG)", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/ElastiCache", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "CPU", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "dark-yellow", "mode": "fixed"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 32, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "displayName": "No. of items", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 11, "y": 94}, "id": 66, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"CacheClusterId": "ws-0001-001"}, "expression": "", "hide": true, "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CurrItems", "metricQueryType": 0, "namespace": "AWS/ElastiCache", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"CacheClusterId": "ws-0001-002"}, "expression": "", "hide": true, "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CurrItems", "metricQueryType": 0, "namespace": "AWS/ElastiCache", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "$A + $B", "hide": false, "refId": "C", "type": "math"}], "title": "No of Items stored", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "semi-dark-orange", "mode": "fixed"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 28, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 11, "x": 0, "y": 102}, "id": 67, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"CacheClusterId": "ws-0001-001"}, "expression": "", "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "DatabaseMemoryUsagePercentage", "metricQueryType": 0, "namespace": "AWS/ElastiCache", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Maximum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"CacheClusterId": "ws-0001-002"}, "expression": "", "hide": false, "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "DatabaseMemoryUsagePercentage", "metricQueryType": 0, "namespace": "AWS/ElastiCache", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "Memory", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 22, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "displayName": "Average", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 11, "y": 102}, "id": 68, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "dimensions": {"CacheClusterId": "ws-0001-001"}, "expression": "", "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CacheHitRate", "metricQueryType": 0, "namespace": "AWS/ElastiCache", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "<PERSON><PERSON> Hit Rate", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Caddy Application State", "uid": "qK7MsL2Vz", "version": 29, "weekStart": ""}