{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 8, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 13, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Cluster", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "Indicates shard allocation status.", "fieldConfig": {"defaults": {"color": {"fixedColor": "light-blue", "mode": "fixed"}, "mappings": [], "max": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 10, "w": 2, "x": 0, "y": 1}, "id": 2, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "Red", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "ClusterStatus.red", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "Yellow", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"DomainName": "$cluster"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "ClusterStatus.yellow", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "Green (Healthy)", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"DomainName": "$cluster"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "ClusterStatus.green", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Cluster Status", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The total number of nodes for your cluster. This metric can increase—often doubling—during configuration changes or service software updates.", "fieldConfig": {"defaults": {"color": {"fixedColor": "light-blue", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 2, "y": 1}, "id": 15, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/^Nodes$/", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "Nodes", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Total Nodes", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "\t\nIndicates whether your cluster is accepting or blocking incoming write requests. A value of 0 means that the cluster is accepting requests. A value of 1 means that it is blocking requests.", "fieldConfig": {"defaults": {"color": {"fixedColor": "light-blue", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 4, "y": 1}, "id": 20, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"titleSize": 14}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"DomainName": "$cluster"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "ClusterIndexWritesBlocked", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Cluster Write Status", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The total used space for the cluster. You must leave the period at one minute to get an accurate value.", "fieldConfig": {"defaults": {"color": {"fixedColor": "light-blue", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 7, "y": 1}, "id": 19, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"titleSize": 14}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"DomainName": "$cluster"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "KibanaHealthyNodes", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Kibana Healthy nodes", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The total used space for the cluster. You must leave the period at one minute to get an accurate value.", "fieldConfig": {"defaults": {"color": {"fixedColor": "light-blue", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}, "unit": "decmbytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 10, "y": 1}, "id": 4, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"titleSize": 14}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"DomainName": "$cluster"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "ClusterUsedSpace", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Minimum"}], "title": "Cluster Used Storage", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The total number of searchable documents across all data nodes in the cluster.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 12, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "locale"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "SearchableDocuments"}, "properties": [{"id": "color", "value": {"fixedColor": "light-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 11, "x": 13, "y": 1}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "SearchableDocuments", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "SearchableDocuments", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The free space for data nodes in the cluster.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 13, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}, "unit": "decmbytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "1vtiZ6IzSn6ar4-Od9EIPg"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Oeyu5CslTliFXGM52wl8Dg"}, "properties": [{"id": "color", "value": {"fixedColor": "light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "vC9xS3azSJCDiESdHdJttQ"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "FWehAcjJRRyuZIxi9_2DDA"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "VurAOWTMSgeLKIwBYSHPXw"}, "properties": [{"id": "color", "value": {"fixedColor": "light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "nh7fdTwNRdy5l7jjF4L1HA"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 11, "x": 2, "y": 5}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster", "NodeId": "*"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "FreeStorageSpace", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Minimum"}], "title": "Cluster Free Storage Space (GB)", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The total number of documents marked for deletion across all data nodes in the cluster. These documents no longer appear in search results, but OpenSearch only removes deleted documents from disk during segment merges. This metric increases after delete requests and decreases after segment merges.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 14, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "locale"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "DeletedDocuments"}, "properties": [{"id": "color", "value": {"fixedColor": "light-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 11, "x": 13, "y": 6}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "DeletedDocuments", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Deleted Documents", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 22, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Performance Indicators", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of requests to a domain, aggregated by HTTP response code (2xx, 3xx, 4xx, 5xx).", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd", "seriesBy": "last"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": -1, "drawStyle": "line", "fillOpacity": 11, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "string"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "2xx"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "3xx"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "4xx"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "5xx"}, "properties": [{"id": "color", "value": {"fixedColor": "light-purple", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 12}, "id": 27, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "2xx", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "2xx", "region": "default", "sql": {"from": {"name": "SCHEMA", "parameters": [{"name": "AWS/ES", "type": "functionParameter"}, {"name": "DomainName", "type": "functionParameter"}], "type": "function"}, "select": {"name": "SUM", "parameters": [{"name": "$cluster", "type": "functionParameter"}], "type": "function"}}, "sqlExpression": "SELECT SUM($cluster) FROM SCHEMA(\"AWS/ES\", DomainName)", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "3xx", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "3xx", "region": "default", "sql": {"from": {"name": "SCHEMA", "parameters": [{"name": "AWS/ES", "type": "functionParameter"}, {"name": "DomainName", "type": "functionParameter"}], "type": "function"}, "select": {"name": "SUM", "parameters": [{"name": "$cluster", "type": "functionParameter"}], "type": "function"}}, "sqlExpression": "SELECT SUM($cluster) FROM SCHEMA(\"AWS/ES\", DomainName)", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "4xx", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "4xx", "region": "default", "sql": {"from": {"name": "SCHEMA", "parameters": [{"name": "AWS/ES", "type": "functionParameter"}, {"name": "DomainName", "type": "functionParameter"}], "type": "function"}, "select": {"name": "SUM", "parameters": [{"name": "$cluster", "type": "functionParameter"}], "type": "function"}}, "sqlExpression": "SELECT SUM($cluster) FROM SCHEMA(\"AWS/ES\", DomainName)", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "5xx", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "5xx", "region": "default", "sql": {"from": {"name": "SCHEMA", "parameters": [{"name": "AWS/ES", "type": "functionParameter"}, {"name": "DomainName", "type": "functionParameter"}], "type": "function"}, "select": {"name": "SUM", "parameters": [{"name": "$cluster", "type": "functionParameter"}], "type": "function"}}, "sqlExpression": "SELECT SUM($cluster) FROM SCHEMA(\"AWS/ES\", DomainName)", "statistic": "Sum"}], "title": "HTTP Response Codes", "transformations": [{"id": "groupBy", "options": {"fields": {"ElasticsearchRequests": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "Time": {"aggregations": ["count"], "operation": "aggregate"}}}}], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of HTTP requests made to the OpenSearch cluster that included an invalid (or missing) host header.\n\nValid requests include the domain hostname as the host header value. OpenSearch Service rejects invalid requests for public access domains that don't have a restrictive access policy.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 38, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "string"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "InvalidHostHeaderRequests"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ElasticsearchRequests_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-purple", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 12}, "id": 28, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "InvalidHostHeaderRequests", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "ElasticsearchRequests", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Invalid Host Header", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of indexing operations per minute. A single call to the _bulk API that adds two documents and updates two counts as four operations, which might be spread across one or more nodes. If that index has one or more replicas, other nodes in the cluster also record a total of four indexing operations. Document deletions do not count towards this metric.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 30, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Oeyu5CslTliFXGM52wl8Dg"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1vtiZ6IzSn6ar4-Od9EIPg"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "vC9xS3azSJCDiESdHdJttQ"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1M_3KcPQT3GrFoUGKtZtPg"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "VurAOWTMSgeLKIwBYSHPXw"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "nh7fdTwNRdy5l7jjF4L1HA"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "FWehAcjJRRyuZIxi9_2DDA"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 17}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster", "NodeId": "*"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "IndexingRate", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Minimum"}], "title": "Indexing rate (ops per minute)", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The average time, in milliseconds, that it takes a shard to complete an indexing operation.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 30, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Oeyu5CslTliFXGM52wl8Dg"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1vtiZ6IzSn6ar4-Od9EIPg"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "vC9xS3azSJCDiESdHdJttQ"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1M_3KcPQT3GrFoUGKtZtPg"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "VurAOWTMSgeLKIwBYSHPXw"}, "properties": [{"id": "color", "value": {"fixedColor": "light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "nh7fdTwNRdy5l7jjF4L1HA"}, "properties": [{"id": "color", "value": {"fixedColor": "light-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 17}, "id": 24, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster", "NodeId": "*"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "IndexingLatency", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Indexing Latency (ms)", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The total number of search requests per minute for all shards on a data node. A single call to the _search API might return results from many different shards. If five of these shards are on one node, the node would report 5 for this metric, even though the client only made one request.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 30, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green"}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Oeyu5CslTliFXGM52wl8Dg"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1vtiZ6IzSn6ar4-Od9EIPg"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "vC9xS3azSJCDiESdHdJttQ"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1M_3KcPQT3GrFoUGKtZtPg"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "VurAOWTMSgeLKIwBYSHPXw"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "nh7fdTwNRdy5l7jjF4L1HA"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "FWehAcjJRRyuZIxi9_2DDA"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 23}, "id": 25, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster", "NodeId": "*"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "SearchRate", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Minimum"}], "title": "Search rate (ops per min)", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The average time, in milliseconds, that it takes a shard on a data node to complete a search operation.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 30, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green"}]}, "unit": "ms"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Oeyu5CslTliFXGM52wl8Dg"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1vtiZ6IzSn6ar4-Od9EIPg"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "vC9xS3azSJCDiESdHdJttQ"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "1M_3KcPQT3GrFoUGKtZtPg"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "VurAOWTMSgeLKIwBYSHPXw"}, "properties": [{"id": "color", "value": {"fixedColor": "light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "nh7fdTwNRdy5l7jjF4L1HA"}, "properties": [{"id": "color", "value": {"fixedColor": "light-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 23}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster", "NodeId": "*"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "SearchLatency", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Search Latency (ms)", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 29}, "id": 11, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Data Nodes", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "CPU Utilisation of all nodes in the cluster", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 38, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "FWehAcjJRRyuZIxi9_2DDA"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "VurAOWTMSgeLKIwBYSHPXw"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "nh7fdTwNRdy5l7jjF4L1HA"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 9, "x": 0, "y": 30}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster", "NodeId": "*"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Minimum"}], "title": "Node CPU Utilisation (%)", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The maximum percentage of the Java heap used for all data nodes in the cluster. OpenSearch Service uses half of an instance's RAM for the Java heap, up to a heap size of 32 GiB. You can scale instances vertically up to 64 GiB of RAM, at which point you can scale horizontally by adding instances.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd", "seriesBy": "last"}, "custom": {"axisLabel": "", "axisPlacement": "left", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 12, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "VurAOWTMSgeLKIwBYSHPXw"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 9, "x": 9, "y": 30}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClientId": "************", "DomainName": "$cluster", "NodeId": "*"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "JVMMemoryPressure", "metricQueryType": 0, "namespace": "AWS/ES", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "JVM Pressure (%)", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": "installs", "value": "installs"}, "description": "Choose the cluster you wish to view metrics for.", "hide": 0, "includeAll": false, "multi": false, "name": "cluster", "options": [{"selected": true, "text": "installs", "value": "installs"}, {"selected": false, "text": "logging", "value": "logging"}, {"selected": false, "text": "logging-comms", "value": "logging-comms"}, {"selected": false, "text": "pod-units", "value": "pod-units"}, {"selected": false, "text": "chargepoints", "value": "chargepoints"}, {"selected": false, "text": "end-of-line-prod", "value": "end-of-line-prod"}], "query": "installs,logging,logging-comms,pod-units,chargepoints,end-of-line-prod", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ES Prod clusters", "uid": "LOr9mW-7k", "version": 8, "weekStart": ""}