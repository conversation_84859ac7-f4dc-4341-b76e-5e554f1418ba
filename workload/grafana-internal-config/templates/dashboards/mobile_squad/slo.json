{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 267, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Driver", "type": "row"}, {"gridPos": {"h": 5, "w": 6, "x": 0, "y": 1}, "id": 2, "libraryPanel": {"name": "Mobile API Availability 99.9%", "uid": "LibPanMobApiAv"}, "title": "Mobile API Availability 99.9%"}, {"gridPos": {"h": 5, "w": 6, "x": 6, "y": 1}, "id": 4, "libraryPanel": {"name": "Mobile API Latency < 500ms 99.9%", "uid": "LibPanMobApiLat"}, "title": "Mobile API Latency < 500ms 99.9%"}, {"gridPos": {"h": 5, "w": 6, "x": 12, "y": 1}, "id": 14, "libraryPanel": {"name": "Opencharge Web App Availability 99.9%", "uid": "LibPanOpCharWebAppAv"}, "title": "Opencharge Web App Availability 99.9%"}, {"gridPos": {"h": 5, "w": 6, "x": 18, "y": 1}, "id": 16, "libraryPanel": {"name": "Opencharge Web App Latency < 15s 99.9%", "uid": "LibPanOpCharWebAppLat"}, "title": "Opencharge Web App Latency < 15s 99.9%"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 24, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Installer", "type": "row"}, {"gridPos": {"h": 5, "w": 6, "x": 0, "y": 7}, "id": 30, "libraryPanel": {"name": "Installer BFF Availability 99.9%", "uid": "ads5i773eopa8a"}, "title": "Installer BFF Availability 99.9%"}, {"gridPos": {"h": 5, "w": 6, "x": 6, "y": 7}, "id": 32, "libraryPanel": {"name": "Installer BFF Latency < 500ms 99.9%", "uid": "ads5i7yuk2kg0c"}, "title": "Installer BFF Latency < 500ms 99.9%"}, {"gridPos": {"h": 5, "w": 6, "x": 12, "y": 7}, "id": 6, "libraryPanel": {"name": "Installer API Availability 99.9%", "uid": "LibPanInstApiAv"}, "title": "Installer API Availability 99.9%"}, {"gridPos": {"h": 5, "w": 6, "x": 18, "y": 7}, "id": 8, "libraryPanel": {"name": "Installer API Latency < 500ms 99.9%", "uid": "LibPanInstApiLat"}, "title": "Installer API Latency < 500ms 99.9%"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 26, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Authorisation", "type": "row"}, {"gridPos": {"h": 5, "w": 6, "x": 0, "y": 13}, "id": 10, "libraryPanel": {"name": "Driver Account API Availability 99.9%", "uid": "LibPanDriAccApiAv"}, "title": "Driver Account API Availability 99.9%"}, {"gridPos": {"h": 5, "w": 6, "x": 6, "y": 13}, "id": 12, "libraryPanel": {"name": "Driver Account API Latency < 500ms 99.9%", "uid": "LibPanDriAccApiLat"}, "title": "Driver Account API Latency < 500ms 99.9%"}, {"gridPos": {"h": 5, "w": 6, "x": 12, "y": 13}, "id": 18, "libraryPanel": {"name": "Driver Account Web App Availability 99.9%", "uid": "LibPanDriAccWebAppAv"}, "title": "Driver Account Web App Availability 99.9%"}, {"gridPos": {"h": 5, "w": 6, "x": 18, "y": 13}, "id": 20, "libraryPanel": {"name": "Driver Account Web App Latency < 500ms 99.9%", "uid": "LibPanDriAccWebAppLat"}, "title": "Driver Account Web App Latency < 500ms 99.9%"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 33, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 19}, "id": 28, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "id": "", "logGroupNames": [], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default"}], "title": "Panel Title", "type": "stat"}], "title": "WIP", "type": "row"}], "refresh": "", "revision": 1, "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "destination-prod-access-logs", "value": "AthenaDestProdAccLog"}, "hide": 0, "includeAll": false, "multi": false, "name": "Athena", "options": [], "query": "grafana-athena-datasource", "queryValue": "", "refresh": 1, "regex": "/^destination/", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": false, "text": "prod", "value": "opencharge_web_app_prod"}, "hide": 0, "includeAll": false, "multi": false, "name": "database", "options": [{"selected": true, "text": "prod", "value": "opencharge_web_app_prod"}, {"selected": false, "text": "staging", "value": "opencharge_web_app_staging"}, {"selected": false, "text": "dev", "value": "opencharge_web_app_dev"}], "query": "prod : opencharge_web_app_prod, staging : opencharge_web_app_staging, dev : opencharge_web_app_dev", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Mobile SLOs", "uid": "XHQoPIiIk", "version": 3, "weekStart": ""}