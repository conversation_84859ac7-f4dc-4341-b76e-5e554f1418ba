{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 155, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "R200s"}, "properties": [{"id": "displayName", "value": "200s"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "id"}, "properties": [{"id": "custom.width", "value": 181}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "time"}, "properties": [{"id": "custom.width", "value": 258}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "src"}, "properties": [{"id": "custom.width", "value": 206}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "@message"}, "properties": [{"id": "custom.width", "value": 176}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 166}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "message"}, "properties": [{"id": "custom.width", "value": 390}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "status"}, "properties": [{"id": "custom.width", "value": 74}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "bytes"}, "properties": [{"id": "custom.width", "value": 163}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "responseTime"}, "properties": [{"id": "custom.width", "value": 282}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "verb"}, "properties": [{"id": "custom.width", "value": 256}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "path"}, "properties": [{"id": "custom.width"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "http_method"}, "properties": [{"id": "custom.width", "value": 279}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "level"}, "properties": [{"id": "custom.width", "value": 200}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "request_id"}, "properties": [{"id": "custom.width", "value": 274}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "date_time"}, "properties": [{"id": "custom.width", "value": 282}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 0}, "id": 3, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "expression": "fields @timestamp, @message\n| parse @message \"time=* level=* request_id=* http.method=* http.url=* http.remote_addr=* http.status=* http.bytes=* http.time_ms=*\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter url like '${request_path}' and url not like 'health'\n| sort by date_time desc\n| display date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms", "hide": false, "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "2xx", "region": "default", "statsGroups": ["date_time", "desc"]}], "title": "Recent requests", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 10}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "expression": "fields @timestamp, @message\n| parse @message \"time=* level=* request_id=* http.method=* http.url=* http.remote_addr=* http.status=* http.bytes=* http.time_ms=*\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter status >= 200 and status < 300\n| filter url like '${request_path}' and url not like 'health'\n| stats count(*) as count200s by bin(30m)", "hide": false, "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "2xx", "region": "default", "statsGroups": ["bin(30m)"]}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "expression": "fields @timestamp, @message\n| parse @message \"time=* level=* request_id=* http.method=* http.url=* http.remote_addr=* http.status=* http.bytes=* http.time_ms=*\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter status >= 300 and status < 400\n| filter url like '${request_path}' and url not like 'health'\n| stats count(*) as count300s by bin(30m)", "hide": false, "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "3xx", "region": "default", "statsGroups": ["bin(30m)"]}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "expression": "fields @timestamp, @message\n| parse @message \"time=* level=* request_id=* http.method=* http.url=* http.remote_addr=* http.status=* http.bytes=* http.time_ms=*\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter status >= 400 and status < 500\n| filter url like '${request_path}' and url not like 'health'\n| stats count(*) as count400s by bin(30m)", "hide": false, "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "4xx", "region": "default", "statsGroups": ["bin(30m)"]}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "expression": "fields @timestamp, @message\n| parse @message \"time=* level=* request_id=* http.method=* http.url=* http.remote_addr=* http.status=* http.bytes=* http.time_ms=*\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter status >= 500\n| filter url like '${request_path}' and url not like 'health'\n| stats count(*) as count500s by bin(30m)", "hide": false, "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "5xx", "region": "default", "statsGroups": ["bin(30m)"]}], "title": "HTTP Codes Count", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 295}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 10}, "id": 4, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "expression": "fields @timestamp, @message\n| filter @message like /level=error/\n| parse @message \"* * * *\" as date_time, level, request_id, err\n| sort by date_time desc\n| display date_time, level, request_id, err", "hide": false, "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "2xx", "region": "default", "statsGroups": ["date_time", "desc"]}], "title": "Errors", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "requests"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "lcd", "type": "gauge"}}, {"id": "custom.width", "value": 184}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "path"}, "properties": [{"id": "custom.width", "value": 710}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 20}, "id": 5, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "expression": "fields @timestamp, @message\n| parse @message \"time=* level=* request_id=* http.method=* http.url=* http.remote_addr=* http.status=* http.bytes=* http.time_ms=*\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter ispresent(url) and url like '${request_path}' and url not like 'health'\n| stats count(*) as requests by url\n| sort requests desc", "hide": false, "id": "", "logGroups": [{"accountId": "************", "arn": "arn:aws:logs:eu-west-1:************:log-group:/ecs/data-platform-api:*", "name": "/ecs/data-platform-api"}], "namespace": "", "queryMode": "Logs", "refId": "2xx", "region": "default", "statsGroups": ["url"]}], "title": "Popular paths", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "url"}, "properties": [{"id": "custom.width", "value": 878}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 20}, "id": 7, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "expression": "fields @timestamp, @message\n| parse @message \"time=* level=* request_id=* http.method=* http.url=* http.remote_addr=* http.status=* http.bytes=* http.time_ms=*\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter url like '${request_path}' and url not like 'health'\n| sort time_ms desc\n| display url, time_ms", "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "Slowest endpoints", "region": "default", "statsGroups": []}], "title": "Slowest requests", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["Received"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 29}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "Deleted", "datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "dimensions": {"QueueName": "charge-commands"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesDeleted", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "Received", "datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "dimensions": {"QueueName": "charge-commands"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesReceived", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Charge commands throughput", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["Received"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 29}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "Deleted", "datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "dimensions": {"QueueName": "charge-events"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesDeleted", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "Received", "datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "dimensions": {"QueueName": "charge-events"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesReceived", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Charge events throughput", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "label": "Request path", "name": "request_path", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Data Platform API Service", "uid": "ubvizrQ4k", "version": 2, "weekStart": ""}