{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 264, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateStag"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 22, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateStag"}, "measure": "", "rawQuery": "with half_hours as (\n  select distinct\n    time\n  from \"energy_metrics\".\"energy_metrics_by_30_minutes\"\n  where $__timeFilter\n  and day_of_week(time) >= 1 and day_of_week(time) <= 5\n  order by time\n),\nppid_energy_data as (\n  select \n    hh.time, \n    if(e.energy > 0, e.energy, 0) as energy\n  from half_hours hh\n  left join \"energy_metrics\".\"energy_metrics_by_30_minutes\" e\n    on hh.time = e.time\n    and e.ppid = '$ppid'\n)\nselect hour(time) as hour, minute(time) as minute, avg(energy) as avg_energy\nfrom ppid_energy_data\ngroup by hour(time), minute(time)\norder by hour, minute", "refId": "A"}], "title": "Weekday Baseline", "type": "table"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateStag"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 22, "w": 12, "x": 12, "y": 0}, "id": 4, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "time"}]}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateStag"}, "measure": "", "rawQuery": "select uui, \n  ppid,\n  time,\n  day_of_week(time) as day_of_week,\n  hour(time) as hour,\n  minute(time) as minute, \n  energy as energy\nfrom \"energy_metrics\".energy_metrics_by_30_minutes\nwhere $__timeFilter\nand day_of_week(time) >= 1 and day_of_week(time) <= 5\nand ppid = '$ppid'\nand energy > 0\nORDER BY time\n", "refId": "A"}], "title": "Weekday Charges", "type": "table"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateStag"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 22, "w": 12, "x": 0, "y": 22}, "id": 3, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateStag"}, "measure": "", "rawQuery": "with half_hours as (\n  select distinct\n    time\n  from \"energy_metrics\".\"energy_metrics_by_30_minutes\"\n  where $__timeFilter\n  and day_of_week(time) = 6 or day_of_week(time) = 7\n  order by time\n),\nppid_energy_data as (\n  select \n    hh.time, \n    if(e.energy > 0, e.energy, 0) as energy\n  from half_hours hh\n  left join \"energy_metrics\".\"energy_metrics_by_30_minutes\" e\n    on hh.time = e.time\n    and e.ppid = '$ppid'\n)\nselect hour(time) as hour, minute(time) as minute, avg(energy) as avg_energy\nfrom ppid_energy_data\ngroup by hour(time), minute(time)\norder by hour, minute", "refId": "A"}], "title": "Weekend Baseline", "type": "table"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateStag"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 22, "w": 12, "x": 12, "y": 22}, "id": 5, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateStag"}, "measure": "", "rawQuery": "select uui,\n  ppid,\n  time, \n  hour(time) as hour,\n  minute(time) as minute, \n  energy as energy\nfrom \"energy_metrics\".energy_metrics_by_30_minutes\nwhere $__timeFilter\nand (day_of_week(time) = 6 or day_of_week(time) = 7)\nand ppid = '$ppid'\nand energy > 0\nORDER BY time\n", "refId": "A"}], "title": "Weekend Charges", "type": "table"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "PSL-608320", "value": "PSL-608320"}, "hide": 0, "name": "ppid", "options": [{"selected": true, "text": "PSL-608320", "value": "PSL-608320"}], "query": "PSL-608320", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-14d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "<PERSON><PERSON> (staging)", "uid": "zEUZCNBSk", "version": 13, "weekStart": ""}