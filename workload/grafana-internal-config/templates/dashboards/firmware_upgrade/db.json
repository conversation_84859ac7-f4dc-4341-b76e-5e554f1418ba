{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 68, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"DBClusterIdentifier": "firmware-upgrade-prod", "Role": "*"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/RDS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "thresholds": [{"colorMode": "critical", "op": "gt", "value": 60, "visible": true}], "title": "CPU Utilisation", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"DBClusterIdentifier": "firmware-upgrade-prod", "Role": "*"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Deadlocks", "metricQueryType": 0, "namespace": "AWS/RDS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "Deadlock Detection", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"DBClusterIdentifier": "firmware-upgrade-prod", "Role": "*"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "DatabaseConnections", "metricQueryType": 0, "namespace": "AWS/RDS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "DB Connections", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"DBClusterIdentifier": "firmware-upgrade-prod", "Role": "*"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUCreditBalance", "metricQueryType": 0, "namespace": "AWS/RDS", "period": "5m", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "CPU Credit Balance", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 18}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"DBClusterIdentifier": "firmware-upgrade-prod", "Role": "*"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "WriteIOPS", "metricQueryType": 0, "namespace": "AWS/RDS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Write IOPS", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Firmware Upgrade DB", "uid": "i2YQrLDVk", "version": 14, "weekStart": ""}