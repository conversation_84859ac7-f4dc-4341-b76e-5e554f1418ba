{"datasource": "pod-point", "description": "The number of messages available for retrieval from the queue.\n\nReporting Criteria: A non-negative value is reported if the queue is active.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": -1, "drawStyle": "line", "fillOpacity": 34, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 7, "x": 17, "y": 51}, "id": 23763571993, "libraryPanel": {"name": "SQS - Approximate Number Of Messages Visible", "uid": "LibPanSqsVisMessNum", "version": 2}, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "{{label}}", "dimensions": {"QueueName": "$sqs_queues"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateNumberOfMessagesVisible", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "SampleCount"}], "title": "Approximate Number Of Messages Visible", "transformations": [], "type": "timeseries"}