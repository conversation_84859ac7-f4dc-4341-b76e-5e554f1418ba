{"datasource": "pod-point", "description": "The number of connections that were not successfully established between the load balancer and target. This metric does not apply if the target is a Lambda function.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "DatabaseConnections"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}, {"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["targetgroup/partners-pre-prod-service-tg/6a3c119f2ece0c49"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 4, "w": 12, "x": 0, "y": 12}, "id": 23763571993, "libraryPanel": {"name": "EC2 - Target Connection Error Count", "uid": "LibPanEc2TargConnErrCnt", "version": 2}, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "dimensions": {"LoadBalancer": "$service_alb", "TargetGroup": "targetgroup/$service_tg"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "TargetConnectionErrorCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Target Connection Error Count", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "TargetGroup"}}], "type": "timeseries"}