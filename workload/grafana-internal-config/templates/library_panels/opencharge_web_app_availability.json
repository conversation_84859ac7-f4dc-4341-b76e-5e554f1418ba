{"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 0.999}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 1}, "id": 14, "libraryPanel": {"description": "", "name": "Opencharge Web App Availability 99.9%", "type": "stat", "uid": "LibPanOpCharWebAppAv", "version": 7}, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/^actual_SLO$/", "values": true}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "$database", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select 0.999 as target_SLO, cast(x.success as double) / cast(y.total as double) as actual_SLO\n\nfrom (\n  select\n  count(*) as success\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  and elb_status_code < 500\n) x\njoin (\n  select\n  count(*) as total\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n) y\non 1=1", "refId": "A", "table": "alb_access_logs"}], "title": "Opencharge Web App Availability 99.9%", "type": "stat"}