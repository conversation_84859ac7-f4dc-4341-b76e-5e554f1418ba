provider "aws" {
  region = "eu-west-1"

  assume_role {
    role_arn     = "arn:aws:iam::235716190788:role/terraform-ci"
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "grafana-internal-config"
      "pp:terraformWorkspace"               = "technology/grafana-internal-config"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

data "aws_ssm_parameter" "workspace_endpoint" {
  name = "/token_auto_rotate_identifer/endpoint_url"
}

data "aws_secretsmanager_secret" "sa_api_token" {
  name = "amg-token-auto-rotate"
}

data "aws_secretsmanager_secret_version" "sa_api_token" {
  secret_id     = data.aws_secretsmanager_secret.sa_api_token.id
  version_stage = "AWSCURRENT"
}

provider "grafana" {
  url  = data.aws_ssm_parameter.workspace_endpoint.value
  auth = jsondecode(data.aws_secretsmanager_secret_version.sa_api_token.secret_string)["token"]
}