# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *       Folderless Dashboards
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "api3_alb" {
  config_json = file("${path.module}/templates/dashboards/general/api3_alb.json")
}

resource "grafana_dashboard" "api3_alb_staging" {
  config_json = file("${path.module}/templates/dashboards/general/api3_alb_staging.json")
}

resource "grafana_dashboard" "ecommerce" {
  config_json = file("${path.module}/templates/dashboards/general/ecommerce.json")
}

resource "grafana_dashboard" "guest_access_revenue_loss" {
  config_json = file("${path.module}/templates/dashboards/general/guest_access_revenue_loss.json")
}

resource "grafana_dashboard" "ocpi_service_prod" {
  config_json = file("${path.module}/templates/dashboards/general/ocpi_service_prod.json")
}

resource "grafana_dashboard" "partners_dealership_tool" {
  config_json = file("${path.module}/templates/dashboards/general/partners_dealership_tool.json")
}

resource "grafana_dashboard" "pdf_manipulator" {
  config_json = file("${path.module}/templates/dashboards/general/pdf_manipulator.json")
}

resource "grafana_dashboard" "rfid_api" {
  config_json = file("${path.module}/templates/dashboards/general/rfid_api.json")
}

resource "grafana_dashboard" "web_app_alb" {
  config_json = file("${path.module}/templates/dashboards/general/web_app_alb.json")
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *      ABB OCPP 1.6 Monitoring
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "abb_ocpp_1_6" {
  config_json = file("${path.module}/templates/dashboards/abb_ocpp_1.6/abb_ocpp_1.6.json")
  folder      = grafana_folder.abb_ocpp_1_6.id
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *          Asset Service
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "asset_service_api" {
  config_json = file("${path.module}/templates/dashboards/asset_service/asset_service_api.json")
  folder      = grafana_folder.asset_service.id
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *     Chargepoint Communications
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "charge_comm_arch1" {
  config_json = file("${path.module}/templates/dashboards/chargepoints_communications/arch1.json")
  folder      = grafana_folder.chargepoints_communications.id
}

resource "grafana_dashboard" "charge_comm_arch2" {
  config_json = file("${path.module}/templates/dashboards/chargepoints_communications/arch2.json")
  folder      = grafana_folder.chargepoints_communications.id
}

resource "grafana_dashboard" "charge_comm_ocpp" {
  config_json = file("${path.module}/templates/dashboards/chargepoints_communications/ocpp.json")
  folder      = grafana_folder.chargepoints_communications.id
}

resource "grafana_dashboard" "charge_comm_ocpp_1_6_debug" {
  config_json = file("${path.module}/templates/dashboards/chargepoints_communications/ocpp_1.6_debug.json")
  folder      = grafana_folder.chargepoints_communications.id
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *   Charger Network Infrastructure
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "cni_api3" {
  config_json = file("${path.module}/templates/dashboards/charger_network_infrastructure/api3.json")
  folder      = grafana_folder.charger_network_infrastructure.id
}

resource "grafana_dashboard" "cni_commissioning_service" {
  config_json = file("${path.module}/templates/dashboards/charger_network_infrastructure/commissioning_service.json")
  folder      = grafana_folder.charger_network_infrastructure.id
}

resource "grafana_dashboard" "cni_es_prod_clusters" {
  config_json = file("${path.module}/templates/dashboards/charger_network_infrastructure/es_prod_clusters.json")
  folder      = grafana_folder.charger_network_infrastructure.id
}

resource "grafana_dashboard" "cni_ota" {
  config_json = file("${path.module}/templates/dashboards/charger_network_infrastructure/ota.json")
  folder      = grafana_folder.charger_network_infrastructure.id
}

resource "grafana_dashboard" "cni_pod_unit_events_prod" {
  config_json = file("${path.module}/templates/dashboards/charger_network_infrastructure/pod_unit_events_prod.json")
  folder      = grafana_folder.charger_network_infrastructure.id
}

resource "grafana_dashboard" "cni_podadmin_aurora" {
  config_json = file("${path.module}/templates/dashboards/charger_network_infrastructure/podadmin_aurora.json")
  folder      = grafana_folder.charger_network_infrastructure.id
}

resource "grafana_dashboard" "cni_rfid_charge_claims" {
  config_json = file("${path.module}/templates/dashboards/charger_network_infrastructure/rfid_charge_claims.json")
  folder      = grafana_folder.charger_network_infrastructure.id
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *        Customer Support
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "cs_solo_dashboard" {
  config_json = file("${path.module}/templates/dashboards/customer_support/solo_dashboard.json")
  folder      = grafana_folder.customer_support.id
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *            Experience
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "experience_access_logs" {
  config_json = file("${path.module}/templates/dashboards/experience/access_logs.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_alerts" {
  config_json = file("${path.module}/templates/dashboards/experience/alerts.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_api3_alb_containerised" {
  config_json = file("${path.module}/templates/dashboards/experience/api3_alb_containerised.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_api3_alb_containerised_staging" {
  config_json = file("${path.module}/templates/dashboards/experience/api3_alb_containerised_staging.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_api3_auth_attempts" {
  config_json = file("${path.module}/templates/dashboards/experience/api3_auth_attempts.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_app_logs_api3" {
  config_json = file("${path.module}/templates/dashboards/experience/app_logs_api3.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_app_logs_data_platform_api" {
  config_json = file("${path.module}/templates/dashboards/experience/app_logs_data_platform_api.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_app_logs_nestjs" {
  config_json = file("${path.module}/templates/dashboards/experience/app_logs_nestjs.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_glance" {
  config_json = file("${path.module}/templates/dashboards/experience/at_a_glance.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_glance_api3_containerised" {
  config_json = file("${path.module}/templates/dashboards/experience/at_a_glance_api3_containerised.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_data_platform_api" {
  config_json = file("${path.module}/templates/dashboards/experience/data_platform_api.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_dms" {
  config_json = file("${path.module}/templates/dashboards/experience/dms.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_ecs" {
  config_json = file("${path.module}/templates/dashboards/experience/ecs.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_elb" {
  config_json = file("${path.module}/templates/dashboards/experience/elb.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_endpoint_performance" {
  config_json = file("${path.module}/templates/dashboards/experience/endpoint_performance.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_revenue_calculation_discrepancies" {
  config_json = file("${path.module}/templates/dashboards/experience/revenue_calculation_discrepancies.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_ses" {
  config_json = file("${path.module}/templates/dashboards/experience/ses.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_system_usage_external" {
  config_json = file("${path.module}/templates/dashboards/experience/system_usage_external.json")
  folder      = grafana_folder.experience.id
}

resource "grafana_dashboard" "experience_system_usage_internal" {
  config_json = file("${path.module}/templates/dashboards/experience/system_usage_internal.json")
  folder      = grafana_folder.experience.id
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *         Firmware Upgrade
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "firmware_upgrade_api" {
  config_json = file("${path.module}/templates/dashboards/firmware_upgrade/api.json")
  folder      = grafana_folder.firmware_upgrade.id
}

resource "grafana_dashboard" "firmware_upgrade_consumer" {
  config_json = file("${path.module}/templates/dashboards/firmware_upgrade/consumer.json")
  folder      = grafana_folder.firmware_upgrade.id
}

resource "grafana_dashboard" "firmware_upgrade_db" {
  config_json = file("${path.module}/templates/dashboards/firmware_upgrade/db.json")

  folder = grafana_folder.firmware_upgrade.id
}

resource "grafana_dashboard" "firmware_upgrade_event_processor" {
  config_json = file("${path.module}/templates/dashboards/firmware_upgrade/event_processor.json")
  folder      = grafana_folder.firmware_upgrade.id
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *           Mobile Squad
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "mobile_squad_api3" {
  config_json = file("${path.module}/templates/dashboards/mobile_squad/api3.json")
  folder      = grafana_folder.mobile_squad.id
}

resource "grafana_dashboard" "mobile_squad_auth_migration" {
  config_json = file("${path.module}/templates/dashboards/mobile_squad/auth_migration.json")
  folder      = grafana_folder.mobile_squad.id
}

resource "grafana_dashboard" "mobile_squad_driver_account_api" {
  config_json = file("${path.module}/templates/dashboards/mobile_squad/driver_account_api.json")
  folder      = grafana_folder.mobile_squad.id
}

resource "grafana_dashboard" "mobile_squad_driver_account_web_app" {
  config_json = file("${path.module}/templates/dashboards/mobile_squad/driver_account_web_app.json")
  folder      = grafana_folder.mobile_squad.id
}

resource "grafana_dashboard" "mobile_squad_installer_api" {
  config_json = file("${path.module}/templates/dashboards/mobile_squad/installer_api.json")
  folder      = grafana_folder.mobile_squad.id
}

resource "grafana_dashboard" "mobile_squad_installer_bff" {
  config_json = file("${path.module}/templates/dashboards/mobile_squad/installer_bff.json")
  folder      = grafana_folder.mobile_squad.id
}

resource "grafana_dashboard" "mobile_squad_api" {
  config_json = file("${path.module}/templates/dashboards/mobile_squad/api.json")
  folder      = grafana_folder.mobile_squad.id
}

resource "grafana_dashboard" "mobile_squad_opencharge_web_app" {
  config_json = file("${path.module}/templates/dashboards/mobile_squad/opencharge_web_app.json")
  folder      = grafana_folder.mobile_squad.id
}

resource "grafana_dashboard" "mobile_squad_slo" {
  config_json = file("${path.module}/templates/dashboards/mobile_squad/slo.json")
  folder      = grafana_folder.mobile_squad.id
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *        Network Connectivity
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "nc_0_energy_short_duration_charges" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/0_energy_short_duration_charges.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_arch1_service_production_ecs" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/arch1_service_production_ecs.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_arch2_Lambda_propogateEventToDevicePilot" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/arch2_Lambda_propogateEventToDevicePilot.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_arch2_lambda_propogateEventToKinesis" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/arch2_lambda_propogateEventToKinesis.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_caddy_application_state" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/caddy_application_state.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_comms_per_device" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/comms_per_device.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_comms_whole_fleet" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/comms_whole_fleet.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_config_ppcp_api_endpoint_and_lambda_pcbConfiguration" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/config_ppcp_api_endpoint_and_lambda_pcbConfiguration.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_cs_ppcp_messages_queue" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/cs_ppcp_messages_queue.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_csr_ppcp_api_endpoint_and_lambda_csrHandler" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/csr_ppcp_api_endpoint_and_lambda_csrHandler.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_dc_overspend" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/dc_overspend.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_device_check_grace_period" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/device_check_grace_period.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_event_ppcp_api_endpoint_and_lambda_pcbCommandResponse" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/event_ppcp_api_endpoint_and_lambda_pcbCommandResponse.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_francesco_sandbox" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/francesco_sandbox.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_fw_upgrade_check_ppcp_api_endpoint_and_lambda_podCheckSoftwareUpdateAvailable" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/fw_upgrade_check_ppcp_api_endpoint_and_lambda_podCheckSoftwareUpdateAvailable.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_grace_period" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/grace_period.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_hil_metrics" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/hil_metrics.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_ignoring_event_for_charge_states" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/ignoring_event_for_charge_states.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_jack_sandbox" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/jack_sandbox.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_negative_balance" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/negative_balance.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_negative_balance_trends" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/negative_balance_trends.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_negative_balance_complex" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/negative_balance_complex.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_negative_duration_charges" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/negative_duration_charges.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_ocpp_logs_staging" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/ocpp_logs_staging.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_pod_comms_service" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/pod_comms_service.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_pod_comms_service_charge_data_auditing" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/pod_comms_service_charge_data_auditing.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_ppcp_api_should_continue_nonprod" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/ppcp_api_should_continue_nonprod.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_ppcp_api_should_continue_unit_view" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/ppcp_api_should_continue_unit_view.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_rfid_claim_ppcp_api_endpoint_and_lambda_pcbRfidChargeAuthorisation" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/rfid_claim_ppcp_api_endpoint_and_lambda_pcbRfidChargeAuthorisation.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_shauna_sandbox" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/shauna_sandbox.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_shauna_sandbox_athena" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/shauna_sandbox_athena.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_should_continue_v3_ppcp_api_endpoint_and_lambda_podShouldContinueCharge" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/should_continue_v3_ppcp_api_endpoint_and_lambda_podShouldContinueCharge.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_should_continue_v4_ppcp_api_endpoint_and_lambda_podShouldContinueChargev4" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/should_continue_v4_ppcp_api_endpoint_and_lambda_podShouldContinueChargev4.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_time_ppcp_api_endpoint_and_lambda_podTime" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/time_ppcp_api_endpoint_and_lambda_podTime.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_unbilled_charges_by_group" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/unbilled_charges_by_group.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_rds_metrics" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/rds_metrics.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_connectivity_ecs_metrics_prod" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/connectivity_service_ecs_metrics_prod.json")
  folder      = grafana_folder.network_connectivity.id
}

resource "grafana_dashboard" "nc_connectivity_sqs_dashboard_prod" {
  config_json = file("${path.module}/templates/dashboards/network_connectivity/connectivity_sqs_dashboard_prod.json")
  folder      = grafana_folder.network_connectivity.id
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *          Network Domain
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "nd_arch2_lambda_pcbCommandResponse" {
  config_json = file("${path.module}/templates/dashboards/network_domain/arch2_lambda_pcbCommandResponse.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_arch2_lambda_pcbCommandResponse_logged_errors" {
  config_json = file("${path.module}/templates/dashboards/network_domain/arch2_lambda_pcbCommandResponse_logged_errors.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_charging_optimisation_api" {
  config_json = file("${path.module}/templates/dashboards/network_domain/charging_optimisation_api.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_competitions_api" {
  config_json = file("${path.module}/templates/dashboards/network_domain/competitions_api.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_edf" {
  config_json = file("${path.module}/templates/dashboards/network_domain/edf.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_edf_day_ahead" {
  config_json = file("${path.module}/templates/dashboards/network_domain/edf_day_ahead.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_energy_metrics_estate" {
  config_json = file("${path.module}/templates/dashboards/network_domain/energy_metrics_estate.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_energy_metrics_pipeline_health" {
  config_json = file("${path.module}/templates/dashboards/network_domain/energy_metrics_pipeline_health.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_energy_metrics_spare_debug" {
  config_json = file("${path.module}/templates/dashboards/network_domain/energy_metrics_spare_debug.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_energy_metrics_subsets" {
  config_json = file("${path.module}/templates/dashboards/network_domain/energy_metrics_subsets.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_energy_metrics_unit" {
  config_json = file("${path.module}/templates/dashboards/network_domain/energy_metrics_unit.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_enode_api" {
  config_json = file("${path.module}/templates/dashboards/network_domain/enode_api.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_enode_state_of_change" {
  config_json = file("${path.module}/templates/dashboards/network_domain/enode_state_of_change.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_flex_provider_api" {
  config_json = file("${path.module}/templates/dashboards/network_domain/flex_provider_api.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_logging_cs_connectivity" {
  config_json = file("${path.module}/templates/dashboards/network_domain/logging_cs_connectivity.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_logging_cs_state" {
  config_json = file("${path.module}/templates/dashboards/network_domain/logging_cs_state.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_logging_pod_point" {
  config_json = file("${path.module}/templates/dashboards/network_domain/logging_pod_point.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_monitor_fw_upgrades" {
  config_json = file("${path.module}/templates/dashboards/network_domain/monitor_fw_upgrades.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_signal_strength_rssi" {
  config_json = file("${path.module}/templates/dashboards/network_domain/signal_strength_rssi.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_smart_charging_event_handlers" {
  config_json = file("${path.module}/templates/dashboards/network_domain/smart_charging_event_handlers.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_smart_charging_service_api" {
  config_json = file("${path.module}/templates/dashboards/network_domain/smart_charging_service_api.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_smart_charging_service_api_staging" {
  config_json = file("${path.module}/templates/dashboards/network_domain/smart_charging_service_api_staging.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_smart_charging_service_api_dev" {
  config_json = file("${path.module}/templates/dashboards/network_domain/smart_charging_service_api_dev.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_smart_charging_service_db" {
  config_json = file("${path.module}/templates/dashboards/network_domain/smart_charging_service_db.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_smart_charging_service_db_staging" {
  config_json = file("${path.module}/templates/dashboards/network_domain/smart_charging_service_db_staging.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_smart_charging_service_db_dev" {
  config_json = file("${path.module}/templates/dashboards/network_domain/smart_charging_service_db_dev.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_solo_3s_estate" {
  config_json = file("${path.module}/templates/dashboards/network_domain/solo_3s_estate.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_state_of_charge_aggregated" {
  config_json = file("${path.module}/templates/dashboards/network_domain/state_of_charge_aggregated.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_timestream" {
  config_json = file("${path.module}/templates/dashboards/network_domain/timestream.json")
  folder      = grafana_folder.network_domain.id
}

resource "grafana_dashboard" "nd_triggered_heartbeat_monitor" {
  config_json = file("${path.module}/templates/dashboards/network_domain/triggered_heartbeat_monitor.json")
  folder      = grafana_folder.network_domain.id
}

# /**
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#  *           Network State
#  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# */

resource "grafana_dashboard" "ns_baseline_calcs" {
  config_json = file("${path.module}/templates/dashboards/network_state/baseline_calcs.json")
  folder      = grafana_folder.network_state.id
}

resource "grafana_dashboard" "ns_baseline_calcs_staging" {
  config_json = file("${path.module}/templates/dashboards/network_state/baseline_calcs_staging.json")
  folder      = grafana_folder.network_state.id
}

resource "grafana_dashboard" "ns_diagnostic_service" {
  config_json = file("${path.module}/templates/dashboards/network_state/diagnostic_service.json")
  folder      = grafana_folder.network_state.id
}

resource "grafana_dashboard" "ns_energy_metrics_toolshed_staging" {
  config_json = file("${path.module}/templates/dashboards/network_state/energy_metrics_toolshed_staging.json")
  folder      = grafana_folder.network_state.id
}

resource "grafana_dashboard" "ns_john_baselining_staging" {
  config_json = file("${path.module}/templates/dashboards/network_state/john_baselining_staging.json")
  folder      = grafana_folder.network_state.id
}

resource "grafana_dashboard" "ns_pod_unit_updates_consumer_load_test" {
  config_json = file("${path.module}/templates/dashboards/network_state/pod_unit_updates_consumer_load_test.json")
  folder      = grafana_folder.network_state.id
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *              OCPP Comms
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "grafana_dashboard" "ocpp_comms_1_6" {
  config_json = file("${path.module}/templates/dashboards/ocpp_comms/ocpp_comms_1.6.json")
  folder      = grafana_folder.ocpp_comms.id
}

resource "grafana_dashboard" "ocpp_comms_1_6_staging" {
  config_json = file("${path.module}/templates/dashboards/ocpp_comms/ocpp_comms_1.6_staging.json")
  folder      = grafana_folder.ocpp_comms.id
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *           Ownership Domain
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "grafana_dashboard" "od_unit_linking_v3" {
  config_json = file("${path.module}/templates/dashboards/ownership_domain/unit_linking_v3.json")
  folder      = grafana_folder.ownership_domain.id
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                  PKI
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "grafana_dashboard" "pki_certificates" {
  config_json = file("${path.module}/templates/dashboards/pki/certificates.json")
  folder      = grafana_folder.pki.id
}
