terraform {
  backend "remote" {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"

    workspaces {
      name = "grafana-internal-config"
    }
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.53.0, < 6.0.0"
    }
    grafana = {
      source  = "grafana/grafana"
      version = "3.7.0"
    }
  }

  required_version = ">= 1.5.5"
}
