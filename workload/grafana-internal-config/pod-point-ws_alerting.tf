# ssm parameters are managed in the shared-services workspace.

data "aws_ssm_parameter" "opsgenie_api_keys_this" {
  for_each = toset([
    "experience-ev-driver-squad",
    "experience-commercial-squad",
    "experience-data-platform-squad",
    "network-grid-squad",
    "network-assets-squad"
  ])

  name            = "/grafana/pod-point-ws/notification/opsgenie/api-key/${each.key}"
  with_decryption = true
}

resource "grafana_contact_point" "experience_ev_driver_squad" {
  name = "experience-ev-driver-squad"

  opsgenie {
    api_key           = data.aws_ssm_parameter.opsgenie_api_keys_this["experience-ev-driver-squad"].value
    url               = "https://api.opsgenie.com/v2/alerts"
    auto_close        = true
    override_priority = true
  }
}
resource "grafana_contact_point" "experience_commercial_squad" {
  name = "experience-commercial-squad"

  opsgenie {
    api_key = data.aws_ssm_parameter.opsgenie_api_keys_this["experience-commercial-squad"].value
    url     = "https://api.opsgenie.com/v2/alerts"
  }
}

resource "grafana_contact_point" "experience_data_platform_squad" {
  name = "experience-data-platform-squad"

  opsgenie {
    api_key = data.aws_ssm_parameter.opsgenie_api_keys_this["experience-data-platform-squad"].value
    url     = "https://api.opsgenie.com/v2/alerts"
  }
}

resource "grafana_contact_point" "network_grid_squad" {
  name = "network-grid-squad"

  opsgenie {
    api_key = data.aws_ssm_parameter.opsgenie_api_keys_this["network-grid-squad"].value
    url     = "https://api.opsgenie.com/v2/alerts"
  }
}

resource "grafana_contact_point" "network_assets_squad" {
  name = "network-asset-squad"

  opsgenie {
    api_key = data.aws_ssm_parameter.opsgenie_api_keys_this["network-assets-squad"].value
    url     = "https://api.opsgenie.com/v2/alerts"
  }
}

resource "grafana_notification_policy" "main" {
  contact_point      = "grafana-default-sns"
  group_by           = ["grafana_folder", "alertname"]
  disable_provenance = true # Set this to false if you want to enforce management of the notification policy through Terraform only.

  policy {
    contact_point = grafana_contact_point.experience_data_platform_squad.name

    matcher {
      label = "squad"
      match = "="
      value = "experience-data-platform"
    }
  }

  policy {
    contact_point = grafana_contact_point.experience_ev_driver_squad.name

    matcher {
      label = "squad"
      match = "="
      value = "experience-ev-driver"
    }
  }

  policy {
    contact_point = grafana_contact_point.experience_commercial_squad.name

    matcher {
      label = "squad"
      match = "="
      value = "experience-commercial"
    }
  }

  policy {
    contact_point = grafana_contact_point.network_assets_squad.name

    matcher {
      label = "squad"
      match = "="
      value = "network-assets"
    }
  }

  policy {
    contact_point = grafana_contact_point.network_grid_squad.name

    matcher {
      label = "squad"
      match = "="
      value = "network-grid"
    }
  }
}