resource "grafana_team" "experience-commercial" {
  name  = "experience-commercial"
  email = "<EMAIL>"
  members = [
    # "<PERSON><PERSON><PERSON>@pod-point.com",
    # "<PERSON><PERSON>@pod-point.com",
    # "<PERSON><PERSON>@pod-point.com",
    # "<PERSON><PERSON><PERSON>-<PERSON>@pod-point.com",
    # "<PERSON><PERSON><PERSON>@pod-point.com",
    # "<PERSON><PERSON><PERSON>@pod-point.com",
    # "<PERSON><PERSON>@pod-point.com"
  ]
}

resource "grafana_team" "experience-data-platform" {
  name = "experience-data-platform"
  members = [
    # "<PERSON><PERSON>-<PERSON>@pod-point.com",
    # "<PERSON><PERSON><EMAIL>",
    # "<PERSON><PERSON>@pod-point.com",
    # "<PERSON><PERSON>@pod-point.com",
    # "<PERSON>@pod-point.com",
    # "<PERSON>z.Pawele<PERSON>@pod-point.com"
  ]
}

resource "grafana_team" "experience-extra-admins" {
  name = "experience-extra-admins"
  members = [
    "<PERSON><PERSON>@pod-point.com",
    "<PERSON>@pod-point.com"
  ]

  lifecycle {
    # User management is unsupported using Grafana API tokens
    ignore_changes = [members]
  }
}
