resource "grafana_library_panel" "application_log_count" {
  name       = "Application Logs Count" # Old name: Count of application logs 
  uid        = "LibPanAppLogCount"
  model_json = file("${path.module}/templates/library_panels/application_log_count.json")
}

resource "grafana_library_panel" "chargepoint_messages" {
  name       = "Chargepoint Messages" # Old name: Chargepoint messages 
  uid        = "LibPanChargepointMess"
  model_json = file("${path.module}/templates/library_panels/chargepoint_messages.json")
}

resource "grafana_library_panel" "driver_account_api_availability" {
  name       = "Driver Account API Availability 99.9%"
  uid        = "LibPanDriAccApiAv"
  model_json = file("${path.module}/templates/library_panels/driver_account_api_availability.json")
}

resource "grafana_library_panel" "driver_account_api_latency" {
  name       = "Driver Account API Latency < 500ms 99.9%"
  uid        = "LibPanDriAccApiLat"
  model_json = file("${path.module}/templates/library_panels/driver_account_api_latency.json")
}

resource "grafana_library_panel" "driver_account_web_app_availability" {
  name       = "Driver Account Web App Availability 99.9%" # Old name: Driver Account Webapp Availability 99.9%
  uid        = "LibPanDriAccWebAppAv"
  model_json = file("${path.module}/templates/library_panels/driver_account_web_app_availability.json")
}

resource "grafana_library_panel" "driver_account_web_app_latency" {
  name       = "Driver Account Web App Latency < 500ms 99.9%"
  uid        = "LibPanDriAccWebAppLat"
  model_json = file("${path.module}/templates/library_panels/driver_account_web_app_latency.json")
}

resource "grafana_library_panel" "installer_api_availability" {
  name       = "Installer API Availability 99.9%"
  uid        = "LibPanInstApiAv"
  model_json = file("${path.module}/templates/library_panels/installer_api_availability.json")
}

resource "grafana_library_panel" "installer_api_latency" {
  name       = "Installer API Latency < 500ms 99.9%"
  uid        = "LibPanInstApiLat"
  model_json = file("${path.module}/templates/library_panels/installer_api_latency.json")
}

resource "grafana_library_panel" "mobile_api_availability" {
  name       = "Mobile API Availability 99.9%"
  uid        = "LibPanMobApiAv"
  model_json = file("${path.module}/templates/library_panels/mobile_api_availability.json")
}

resource "grafana_library_panel" "mobile_api_latency" {
  name       = "Mobile API Latency < 500ms 99.9%"
  uid        = "LibPanMobApiLat"
  model_json = file("${path.module}/templates/library_panels/mobile_api_latency.json")
}

resource "grafana_library_panel" "opencharge_web_app_availability" {
  name       = "Opencharge Web App Availability 99.9%"
  uid        = "LibPanOpCharWebAppAv"
  model_json = file("${path.module}/templates/library_panels/opencharge_web_app_availability.json")
}

resource "grafana_library_panel" "opencharge_web_app_latency" {
  name       = "Opencharge Web App Latency < 15s 99.9%"
  uid        = "LibPanOpCharWebAppLat"
  model_json = file("${path.module}/templates/library_panels/opencharge_web_app_latency.json")
}

resource "grafana_library_panel" "ec2_health_nodes" {
  name       = "EC2 - Health Nodes"
  uid        = "LibPanEc2HealthNode"
  model_json = file("${path.module}/templates/library_panels/ec2_health_nodes.json")
}

resource "grafana_library_panel" "ec2_unhealthy_nodes" {
  name       = "EC2 - Unhealthy Nodes"
  uid        = "LibPanEc2UnhealthNode"
  model_json = file("${path.module}/templates/library_panels/ec2_unhealthy_nodes.json")
}

resource "grafana_library_panel" "ec2_http_response_codes" {
  name       = "EC2 - HTTP Response Codes"
  uid        = "LibPanEc2HttpResponse"
  model_json = file("${path.module}/templates/library_panels/ec2_http_response_codes.json")
}

resource "grafana_library_panel" "ec2_target_response_time" {
  name       = "EC2 - Target Response Time (s)"
  uid        = "LibPanEc2TargRespTime"
  model_json = file("${path.module}/templates/library_panels/ec2_target_response_time.json")
}

resource "grafana_library_panel" "ec2_target_connection_error_count" {
  name       = "EC2 - Target Connection Error Count"
  uid        = "LibPanEc2TargConnErrCnt"
  model_json = file("${path.module}/templates/library_panels/ec2_target_connection_error_count.json")
}

resource "grafana_library_panel" "rds_database_connections" {
  name       = "RDS - Database Connections (Count)"
  uid        = "LibPanRdsDbConn"
  model_json = file("${path.module}/templates/library_panels/rds_database_connections.json")
}

resource "grafana_library_panel" "rds_freeable_memory" {
  name       = "RDS - Freeable Memory (MB)"
  uid        = "LibPanRdsFreeMem"
  model_json = file("${path.module}/templates/library_panels/rds_freeable_memory.json")
}

resource "grafana_library_panel" "rds_freeable_storage" {
  name       = "RDS - Free Storage Space (MB)"
  uid        = "LibPanRdsFreeStor"
  model_json = file("${path.module}/templates/library_panels/rds_freeable_storage.json")
}

resource "grafana_library_panel" "rds_queue_depth" {
  name       = "RDS - Queue Depth (Count)"
  uid        = "LibPanRdsQueueDepth"
  model_json = file("${path.module}/templates/library_panels/rds_queue_depth.json")
}

resource "grafana_library_panel" "rds_write_throughput" {
  name       = "RDS - Write Throughput (bytes/sec(SI))"
  uid        = "LibPanRdsWriteThro"
  model_json = file("${path.module}/templates/library_panels/rds_write_throughput.json")
}

resource "grafana_library_panel" "rds_write_latency" {
  name       = "RDS - Write Latency (ms)"
  uid        = "LibPanRdsWriteLat"
  model_json = file("${path.module}/templates/library_panels/rds_write_latency.json")
}

resource "grafana_library_panel" "rds_write_operations" {
  name       = "RDS - Write Operations (iops)"
  uid        = "LibPanRdsWriteOps"
  model_json = file("${path.module}/templates/library_panels/rds_write_operations.json")
}

resource "grafana_library_panel" "rds_read_throughput" {
  name       = "RDS - Read Throughput (bytes/sec(SI))"
  uid        = "LibPanRdsReadThro"
  model_json = file("${path.module}/templates/library_panels/rds_read_throughput.json")
}

resource "grafana_library_panel" "rds_read_latency" {
  name       = "RDS - Read Latency (ms)"
  uid        = "LibPanRdsReadLat"
  model_json = file("${path.module}/templates/library_panels/rds_read_latency.json")
}

resource "grafana_library_panel" "rds_read_operations" {
  name       = "RDS - Read Operations (iops)"
  uid        = "LibPanRdsReadOps"
  model_json = file("${path.module}/templates/library_panels/rds_read_operations.json")
}

resource "grafana_library_panel" "rds_cpu_utilisation" {
  name       = "RDS - CPU Utilisation (%)" # Old name: CPU Utilisation (%)
  uid        = "LibPanRdsCpuUtil"
  model_json = file("${path.module}/templates/library_panels/rds_cpu_utilisation.json")
}

resource "grafana_library_panel" "sqs_sent_message_number" {
  name       = "SQS - Number Of Messages Sent"
  uid        = "LibPanSqsSentMessNum"
  model_json = file("${path.module}/templates/library_panels/sqs_sent_message_number.json")
}

resource "grafana_library_panel" "sqs_oldest_message" {
  name       = "SQS - Approximate Age Of Oldest Message"
  uid        = "LibPanSqsOldMess"
  model_json = file("${path.module}/templates/library_panels/sqs_oldest_message.json")
}

resource "grafana_library_panel" "sqs_visible_message_number" {
  name       = "SQS - Approximate Number Of Messages Visible"
  uid        = "LibPanSqsVisMessNum"
  model_json = file("${path.module}/templates/library_panels/sqs_visible_message_number.json")
}
