/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *          Athena data sources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "grafana_data_source" "athena_auth_service_dev_access_logs" {
  type = "grafana-athena-datasource"
  name = "auth-service-dev-access-logs"
  uid  = "AthenaAuthServDevAccLog"

  json_data_encoded = jsonencode({
    authType       = "default"
    assumeRoleArn  = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion  = "eu-west-1"
    catalog        = "AwsDataCatalog"
    database       = "auth_service_dev"
    workgroup      = "primary"
    outputLocation = "s3://grafana-athena-query-results-pod-point"
  })
}

resource "grafana_data_source" "athena_auth_service_staging_access_logs" {
  type = "grafana-athena-datasource"
  name = "auth-service-staging-access-logs"
  uid  = "AthenaAuthServStagAccLog"

  json_data_encoded = jsonencode({
    authType       = "default"
    assumeRoleArn  = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion  = "eu-west-1"
    catalog        = "AwsDataCatalog"
    database       = "auth_service_staging"
    workgroup      = "primary"
    outputLocation = "s3://grafana-athena-query-results-pod-point"
  })
}

resource "grafana_data_source" "athena_auth_service_prod_access_logs" {
  type = "grafana-athena-datasource"
  name = "auth-service-prod-access-logs"
  uid  = "AthenaAuthServProdAccLog"

  json_data_encoded = jsonencode({
    authType       = "default"
    assumeRoleArn  = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion  = "eu-west-1"
    catalog        = "AwsDataCatalog"
    database       = "auth_service_prod"
    workgroup      = "primary"
    outputLocation = "s3://grafana-athena-query-results-pod-point"
  })
}

resource "grafana_data_source" "athena_cs_connectivity_dev" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - cs-connectivity-dev"
  uid  = "AthenaCsConnDev"

  json_data_encoded = jsonencode({
    authType       = "default"
    assumeRoleArn  = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion  = "eu-west-1"
    catalog        = "AwsDataCatalog"
    database       = "ocpp_logs"
    workgroup      = "ocpp_logs"
    outputLocation = "s3://grafana-athena-query-results-connectivity-dev"
  })
}

resource "grafana_data_source" "athena_cs_connectivity_dev_ocpp_logs" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - cs-connectivity-dev - ocpp_logs"
  uid  = "AthenaCsConnDevOcppLog"

  json_data_encoded = jsonencode({
    authType       = "default"
    assumeRoleArn  = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion  = "eu-west-1"
    catalog        = "AwsDataCatalog"
    database       = "ocpp_logs"
    workgroup      = "ocpp_logs"
    outputLocation = "s3://athena-query-execution-dev"
  })
}

resource "grafana_data_source" "athena_cs_connectivity_staging" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - cs-connectivity-staging"
  uid  = "AthenaCsConnStag"

  json_data_encoded = jsonencode({
    authType       = "default"
    assumeRoleArn  = "arn:aws:iam::001439095820:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion  = "eu-west-1"
    catalog        = "AwsDataCatalog"
    database       = "ocpp_logs"
    workgroup      = "ocpp_logs"
    outputLocation = "s3://grafana-athena-query-results-connectivity-staging"
  })
}

resource "grafana_data_source" "athena_cs_connectivity_staging_ocpp_logs" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - cs-connectivity-staging - ocpp_logs"
  uid  = "AthenaCsConnStagOcppLog"

  json_data_encoded = jsonencode({
    authType       = "default"
    assumeRoleArn  = "arn:aws:iam::001439095820:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion  = "eu-west-1"
    catalog        = "AwsDataCatalog"
    database       = "ocpp_logs"
    workgroup      = "ocpp_logs"
    outputLocation = "s3://athena-query-execution-staging"
  })
}

resource "grafana_data_source" "athena_cs_connectivity_prod" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - cs-connectivity-prod"
  uid  = "AthenaCsConnProd"

  json_data_encoded = jsonencode({
    authType       = "default"
    assumeRoleArn  = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion  = "eu-west-1"
    catalog        = "AwsDataCatalog"
    database       = "ocpp_logs"
    workgroup      = "ocpp_logs"
    outputLocation = "s3://grafana-athena-query-results-connectivity-prod"
  })
}

resource "grafana_data_source" "athena_experience_dev" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - experience dev"
  uid  = "AthenaExpDev"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::146549662676:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
    catalog       = "AwsDataCatalog"
    database      = "default"
    workgroup     = "access_logs"
  })
}

resource "grafana_data_source" "athena_experience_staging" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - experience staging"
  uid  = "AthenaExpStag"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
    catalog       = "AwsDataCatalog"
    database      = "default"
    workgroup     = "access_logs"
  })
}

resource "grafana_data_source" "athena_experience_prod" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - experience prod"
  uid  = "AthenaExpProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::044494356744:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
    catalog       = "AwsDataCatalog"
    database      = "default"
    workgroup     = "access_logs"
  })
}

resource "grafana_data_source" "athena_network_assets_dev" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - network-assets-dev"
  uid  = "AthenaNetAssetDev"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
    catalog       = "AwsDataCatalog"
    database      = "asset_service_api"
    workgroup     = "asset-service-api"
  })
}

resource "grafana_data_source" "athena_network_assets_staging" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - network-assets-staging"
  uid  = "AthenaNetAssetStag"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::033472448514:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
    catalog       = "AwsDataCatalog"
    database      = "asset_service_api"
    workgroup     = "asset-service-api"
  })
}

resource "grafana_data_source" "athena_network_assets_prod" {
  type = "grafana-athena-datasource"
  name = "AWS Athena - network-assets-prod"
  uid  = "AthenaNetAssetProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
    catalog       = "AwsDataCatalog"
    database      = "default"
    workgroup     = "asset-service-api"
  })
}

resource "grafana_data_source" "athena_destination_dev_access_logs" {
  type = "grafana-athena-datasource"
  name = "destination-dev-access-logs"
  uid  = "AthenaDestDevAccLog"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::146549662676:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
    catalog       = "AwsDataCatalog"
    database      = "access_logs"
    workgroup     = "access_logs"
  })
}

resource "grafana_data_source" "athena_destination_stage_access_logs" {
  type = "grafana-athena-datasource"
  name = "destination-stage-access-logs"
  uid  = "AthenaDestStagAccLog"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
    catalog       = "AwsDataCatalog"
    database      = "access_logs"
    workgroup     = "access_logs"
  })
}

resource "grafana_data_source" "athena_destination_prod_access_logs" {
  type = "grafana-athena-datasource"
  name = "destination-prod-access-logs"
  uid  = "AthenaDestProdAccLog"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::044494356744:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
    catalog       = "AwsDataCatalog"
    database      = "access_logs"
    workgroup     = "access_logs"
  })
}

resource "grafana_data_source" "athena_pod_point_workbook" {
  type = "grafana-athena-datasource"
  name = "Pod Point - eu-west-1 primary workbook"
  uid  = "AthenaPodPoint"

  json_data_encoded = jsonencode({
    authType       = "default"
    assumeRoleArn  = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion  = "eu-west-1"
    catalog        = "AwsDataCatalog"
    database       = "loadbalancer_logs"
    workgroup      = "alb-logging"
    outputLocation = "s3://grafana-athena-query-results-pod-point"
  })
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *         Timestream data sources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "grafana_data_source" "timestream_cs_state_dev" {
  type = "grafana-timestream-datasource"
  name = "timestream-cs-state-dev" # old name: Amazon Timestream eu-west-1 ************
  uid  = "TimeStreamCsStateDev"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
    # default macros values
    database = "network_metrics"
    table    = "flex_metrics"
  })
}

resource "grafana_data_source" "timestream_cs_state_staging" {
  type = "grafana-timestream-datasource"
  name = "timestream-cs-state-staging" # old name: Amazon Timestream eu-west-1 ************
  uid  = "TimeStreamCsStateStag"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "timestream_cs_state_prod" {
  type = "grafana-timestream-datasource"
  name = "timestream-cs-state-prod" # old name: Amazon Timestream eu-west-1 ************
  uid  = "TimeStreamCsStateProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *      Opensearch data sources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "grafana_data_source" "opensearch_application_logs" {
  type = "grafana-opensearch-datasource"
  name = "application-logs" # old name: Applications Logs
  url  = "https://search-applications-logs-wszkv6z2shrtb5fj2lu2l72ck4.eu-west-1.es.amazonaws.com/"
  uid  = "OsAppLogs"

  json_data_encoded = jsonencode({
    database                   = "logs-*"
    flavor                     = "elasticsearch"
    version                    = "7.10.2"
    logMessageField            = "message"
    timeField                  = "timestamp"
    maxConcurrentShardRequests = 5
    pplEnabled                 = true
    sigV4AssumeRoleArn         = "arn:aws:iam::************:role/AmazonGrafanaESDataSourceAccess"
    sigV4Auth                  = true
    sigV4AuthType              = "ec2_iam_role"
    sigV4Region                = "eu-west-1"
  })
}

resource "grafana_data_source" "opensearch_logging_comms_67" {
  type = "grafana-opensearch-datasource"
  name = "logging-comms-es-6.7" # old name: Logging Comms (ES 6.7)
  url  = "https://search-logging-comms-akhvx5t65sz7vsr2fqjst4k3me.eu-west-1.es.amazonaws.com"
  uid  = "OsLoggCommsProd"

  json_data_encoded = jsonencode({
    database                   = "comms-*"
    flavor                     = "elasticsearch"
    version                    = "6.7.0"
    timeField                  = "timestamp"
    maxConcurrentShardRequests = 256
    pplEnabled                 = true
    sigV4AssumeRoleArn         = "arn:aws:iam::************:role/AmazonGrafanaESDataSourceAccess"
    sigV4Auth                  = true
    sigV4AuthType              = "ec2_iam_role"
    sigV4Region                = "eu-west-1"
  })
}

resource "grafana_data_source" "opensearch_logging_comms_staging_67" {
  type = "grafana-opensearch-datasource"
  name = "logging-comms-staging-es-6.7" # old name: Logging Comms Staging (ES 6.7)
  url  = "https://search-logging-comms-staging-mo3tdpfv5r5c2ieaqie4dh4fra.eu-west-1.es.amazonaws.com"
  uid  = "OsLoggCommsStag"

  json_data_encoded = jsonencode({
    database                   = "comms-*"
    flavor                     = "elasticsearch"
    version                    = "6.7.0"
    timeField                  = "timestamp"
    maxConcurrentShardRequests = 256
    pplEnabled                 = true
    sigV4AssumeRoleArn         = "arn:aws:iam::************:role/AmazonGrafanaESDataSourceAccess"
    sigV4Auth                  = true
    sigV4AuthType              = "ec2_iam_role"
    sigV4Region                = "eu-west-1"
  })
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *       CloudWatch data sources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "grafana_data_source" "cloudwatch_cs_connectivity_dev" {
  type = "cloudwatch"
  name = "cs-connectivity-dev"
  uid  = "CwCsConnDev"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_cs_connectivity_prod" {
  type = "cloudwatch"
  name = "cs-connectivity-prod"
  uid  = "CwCsConnProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_cs_charge_sessions_dev" {
  type = "cloudwatch"
  name = "cs-charge-sessions-dev"
  uid  = "CwCsChargeSessionsDev"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_cs_charge_sessions_staging" {
  type = "cloudwatch"
  name = "cs-charge-sessions-staging"
  uid  = "CwCsChargeSessionsStaging"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_cs_charge_sessions_prod" {
  type = "cloudwatch"
  name = "cs-charge-sessions-prod"
  uid  = "CwCsChargeSessionsProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_cs_state_dev" {
  type = "cloudwatch"
  name = "cs-state-dev"
  uid  = "CwCsStateDev"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_cs_state_staging" {
  type = "cloudwatch"
  name = "cs-state-staging"
  uid  = "CwCsStateStag"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_cs_state_prod" {
  type = "cloudwatch"
  name = "cs-state-prod"
  uid  = "CwCsStateProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_destination_build" {
  type = "cloudwatch"
  name = "destination-build"
  uid  = "CwDestBuild"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_destination_dev" {
  type = "cloudwatch"
  name = "destination-dev"
  uid  = "CwDestDev"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::146549662676:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_destination_staging" {
  type = "cloudwatch"
  name = "destination-staging"
  uid  = "CwDestStag"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_destination_prod" {
  type = "cloudwatch"
  name = "destination-prod"
  uid  = "CwDestProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::044494356744:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_network_assets_build" {
  type = "cloudwatch"
  name = "network-assets-build"
  uid  = "CwNetAssetBuild"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_network_assets_dev" {
  type = "cloudwatch"
  name = "network-assets-dev"
  uid  = "CwNetAssetDev"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_network_assets_staging" {
  type = "cloudwatch"
  name = "network-assets-staging"
  uid  = "CwNetAssetStag"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::033472448514:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_network_assets_prod" {
  type = "cloudwatch"
  name = "network-assets-prod"
  uid  = "CwNetAssetProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_ownership_data_platform_dev" {
  type = "cloudwatch"
  name = "ownership-data-platform-dev"
  uid  = "CwOwnerDataPlatDev"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_ownership_data_platform_staging" {
  type = "cloudwatch"
  name = "ownership-data-platform-staging"
  uid  = "CwOwnerDataPlatStag"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_ownership_data_platform_prod" {
  type = "cloudwatch"
  name = "ownership-data-platform-prod"
  uid  = "CwOwnerDataPlatProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_pod_point" {
  type = "cloudwatch"
  name = "pod-point" # Old name: Pod Point - eu-west-1
  uid  = "CwPodPoint"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_pp_data_dev" {
  type = "cloudwatch"
  name = "pp-data-dev" # Old name: pp-data-dev - eu-west-1
  uid  = "CwPpDataDev"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_pp_data_prod" {
  type = "cloudwatch"
  name = "pp-data-prod" # Old name: pp-data-prod - eu-west-1
  uid  = "CwPpDataProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::117588945405:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_pp_sandbox" {
  type = "cloudwatch"
  name = "pp-sandbox" # Old name: pp-sandbox - eu-west-1
  uid  = "CwPpSandbox"

  json_data_encoded = jsonencode({
    authType                = "default"
    assumeRoleArn           = "arn:aws:iam::175701924166:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion           = "eu-west-1"
    customMetricsNamespaces = "sandbox"
  })
}

resource "grafana_data_source" "cloudwatch_pp_investors" {
  type = "cloudwatch"
  name = "pp-investors" # Old name: pp-investors - eu-west-1
  uid  = "CwPpInvest"

  json_data_encoded = jsonencode({
    authType                = "default"
    assumeRoleArn           = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion           = "eu-west-1"
    customMetricsNamespaces = "pp-investors"
  })
}

resource "grafana_data_source" "cloudwatch_terraform_enterprise" {
  type = "cloudwatch"
  name = "terraform-enterprise" # Old name: terraform-enterprise - eu-west-2
  uid  = "CwTFE"

  json_data_encoded = jsonencode({
    authType                = "default"
    assumeRoleArn           = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion           = "eu-west-2"
    customMetricsNamespaces = "pp-investors"
  })
}

resource "grafana_data_source" "cloudwatch_vault_non_prod" {
  type = "cloudwatch"
  name = "vault-non-prod" # Old name: vault-non-prod - eu-west-1
  uid  = "CwVaultNonProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::************:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}

resource "grafana_data_source" "cloudwatch_vault_prod" {
  type = "cloudwatch"
  name = "vault-prod" # Old name: vault-prod - eu-west-1
  uid  = "CwVaultProd"

  json_data_encoded = jsonencode({
    authType      = "default"
    assumeRoleArn = "arn:aws:iam::031791189349:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd"
    defaultRegion = "eu-west-1"
  })
}
