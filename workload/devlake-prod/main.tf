locals {
  identifier = "apache-devlake"

  # ? networking
  vpc_cidr_block            = "********/24"
  pod_point_com_hosted_zone = "ZI1YF8KE9MFAW"
  pod_point_com_domain_name = "pod-point.com"
  container_config_ui_port  = 4000
  container_devlake_port    = 8080

  # ? database
  db_port                   = 3306
  db_parameter_group_family = "aurora-mysql8.0"
  db_name                   = "devlake"
  db_user                   = "admin"
  db_password               = jsondecode(data.aws_secretsmanager_secret_version.admin_password.secret_string)["password"]
  db_url                    = "mysql://${local.db_user}:${urlencode(local.db_password)}@${module.aurora.cluster_endpoint}:${local.db_port}/${local.db_name}?charset=utf8mb4&parseTime=True"
  # ? other

  identity_store_id = tolist(data.aws_ssoadmin_instances.sso_instances.identity_store_ids)[0]
  grafana_administrators = [
    "<EMAIL>",
    "<EMAIL>"
  ]

  tags = {}

  default_tags = {
    "pp:environment"                      = "production"
    "pp:domain"                           = "support"
    "pp:owner"                            = "support:engineering-managers"
    "pp:service"                          = "devlake-prod"
    "pp:terraformWorkspace"               = "devlake-prod"
    "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
  }
}

data "aws_caller_identity" "current" {}

data "aws_secretsmanager_secret_version" "oidc_app_secrets" {
  provider  = aws.shared-services
  secret_id = "azure/oidc/apache-devlake"
}

data "aws_ssoadmin_instances" "sso_instances" {
  provider = aws.sso-read
}

data "aws_identitystore_group" "devlake_group" {
  provider = aws.sso-read

  identity_store_id = local.identity_store_id

  alternate_identifier {
    unique_attribute {
      attribute_path  = "DisplayName"
      attribute_value = "Terraform Managed Group - Devlake"
    }
  }
}

data "aws_identitystore_user" "grafana_admins" {
  for_each = toset(local.grafana_administrators)
  provider = aws.sso-read

  identity_store_id = local.identity_store_id

  alternate_identifier {
    unique_attribute {
      attribute_path  = "UserName"
      attribute_value = each.value
    }
  }
}