module "ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "11.3.1"

  identifier         = local.identifier
  repo_names         = ["backstage-podpoint"]
  container_insights = "enabled"
}

module "ecs_service" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "11.3.1"

  identifier         = local.identifier
  service_type       = "rolling"
  cluster_arn        = module.ecs_cluster.arn
  cluster_name       = module.ecs_cluster.name
  pipeline_role_name = module.ecs_cluster.github_role_name

  capacity_fargate_base = 0

  subnet_ids = module.vpc.private_subnets_ids
  vpc_id     = module.vpc.vpc_id

  cpu    = "256"
  memory = "512"

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json

  health_check_grace_period_seconds = 60

  load_balancing_configuration = [
    {
      target_group_arn = module.alb.target_group_arns[0]
      container_name   = "${local.identifier}"
      container_port   = local.container_devlake_port
    },
    {
      target_group_arn = module.alb.target_group_arns[1]
      container_name   = "${local.identifier}-config-ui"
      container_port   = local.container_config_ui_port
    }
  ]

  container_definitions = jsonencode([
    {
      name                     = local.identifier
      image                    = "apache/devlake:v1.0.1"
      essential                = true
      networkMode              = "awsvpc"
      readonly_root_filesystem = false
      portMappings = [
        {
          protocol      = "tcp",
          containerPort = tonumber(local.container_devlake_port)
          hostPort      = tonumber(local.container_devlake_port)
        }
      ],
      linuxParameters = {
        initProcessEnabled = true
      }
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = "/ecs/${local.identifier}"
          awslogs-region        = "eu-west-1"
          awslogs-stream-prefix = "ecs"
        }
      }
      environment = [
        {
          name  = "LOGGING_DIR"
          value = "/app/logs"
        },
        {
          name  = "TZ"
          value = "UTC"
        },
        {
          name  = "PORT"
          value = tostring(local.container_devlake_port)
        },
        {
          name  = "mode"
          value = "release"
        },
        {
          name  = "PLUGIN_DIR" # Lake plugin dir, absolute path or relative path
          value = "bin/plugins"
        },
        {
          name  = "DB_LOGGING_LEVEL"
          value = "Error"
        },
        {
          name  = "DB_URL"
          value = local.db_url
        },
        {
          name  = "API_TIMEOUT"
          value = "120s"
        },
        {
          name  = "API_RETRY"
          value = "3"
        },
        {
          name  = "API_REQUESTS_PER_HOUR"
          value = "10000"
        },
        {
          name  = "PIPELINE_MAX_PARALLEL"
          value = "1"
        },
        {
          name  = "IN_SECURE_SKIP_VERIFY"
          value = "false"
        },
        {
          name  = "FORBID_REDIRECTION"
          value = "false"
        },
        {
          name  = "JIRA_JQL_AUTO_FULL_REFRESH"
          value = "true"
        }
      ]
      secrets = [
        {
          name      = "ENCRYPTION_SECRET",
          valueFrom = aws_secretsmanager_secret.encryption_secret.arn,
        }
      ]
    },
    {
      name                     = "${local.identifier}-config-ui"
      image                    = "apache/devlake-config-ui:v1.0.1"
      essential                = true
      networkMode              = "awsvpc"
      readonly_root_filesystem = false
      dependsOn = [{
        condition     = "START"
        containerName = local.identifier
      }]
      portMappings = [
        {
          protocol      = "tcp"
          containerPort = tonumber(local.container_config_ui_port)
          hostPort      = tonumber(local.container_config_ui_port)
        }
      ]
      linuxParameters = {
        initProcessEnabled = true
      }
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = "/ecs/${local.identifier}"
          awslogs-region        = "eu-west-1"
          awslogs-stream-prefix = "ecs"
        }
      }
      environment = [
        {
          name  = "DEVLAKE_ENDPOINT"
          value = "localhost:${local.container_devlake_port}"
        },
        {
          name  = "USE_EXTERNAL_GRAFANA"
          value = "true"
        },
        {
          name  = "GRAFANA_ENDPOINT"
          value = "https://${module.managed_grafana.workspace_endpoint}"
        },
        {
          name  = "LOGGING_DIR"
          value = "/app/logs"
        },
        {
          name  = "TZ"
          value = "UTC"
        },
        {
          name  = "PORT"
          value = tostring(local.container_devlake_port)
        },
        {
          name  = "mode"
          value = "release"
        },
        {
          name  = "PLUGIN_DIR" # Lake plugin dir, absolute path or relative path
          value = "bin/plugins"
        },
        {
          name  = "DB_LOGGING_LEVEL"
          value = "Error"
        },
        {
          name  = "DB_URL"
          value = local.db_url
        },
        {
          name  = "API_TIMEOUT"
          value = "120s"
        },
        {
          name  = "API_RETRY"
          value = "3"
        },
        {
          name  = "API_REQUESTS_PER_HOUR"
          value = "10000"
        },
        {
          name  = "PIPELINE_MAX_PARALLEL"
          value = "1"
        },
        {
          name  = "IN_SECURE_SKIP_VERIFY"
          value = "false"
        },
        {
          name  = "FORBID_REDIRECTION"
          value = "false"
        },
        {
          name  = "JIRA_JQL_AUTO_FULL_REFRESH"
          value = "true"
        }
      ],
      secrets = [
        {
          name      = "ENCRYPTION_SECRET"
          valueFrom = aws_secretsmanager_secret.encryption_secret.arn
        }
      ]
    }
  ])

  tags = local.tags
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *        Custom IAM Policy
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

data "aws_iam_policy_document" "custom_ecs_task_execution_policy" {
  statement {
    sid = "RetrieveSecretManagerSecretValues"

    actions = ["secretsmanager:GetSecretValue"]

    resources = [
      module.aurora.admin_user_secret_manager_arn,
      aws_secretsmanager_secret.encryption_secret.arn,
    ]
  }

  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]
    resources = [
      aws_kms_key.backend.arn
    ]
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *    Fargate Security Group Rules
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_security_group_rule" "ecs_fargate_ingress_via_loadbalancer" {
  type                     = "ingress"
  description              = "Permits traffic to the standard devlake port number."
  from_port                = local.container_devlake_port
  to_port                  = local.container_devlake_port
  protocol                 = "tcp"
  source_security_group_id = module.alb.security_group_id
  security_group_id        = module.ecs_service.security_group_id
}

resource "aws_security_group_rule" "ecs_fargate_ingress_config_ui_port_via_loadbalancer" {
  type                     = "ingress"
  description              = "Permits traffic to the devlake config ui port number"
  from_port                = local.container_config_ui_port
  to_port                  = local.container_config_ui_port
  protocol                 = "tcp"
  source_security_group_id = module.alb.security_group_id
  security_group_id        = module.ecs_service.security_group_id
}

resource "aws_security_group_rule" "ecs_fargate_allow_all_egress" {
  type              = "egress"
  description       = "Permits all outgoing traffic"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = module.ecs_service.security_group_id
}