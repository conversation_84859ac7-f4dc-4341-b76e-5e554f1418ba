provider "aws" {
  region = var.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.assume_role_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = var.region
  alias  = "pod-point"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pod_point_main_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = "us-east-1"
  alias  = "pod-point-us-east-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pod_point_main_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = var.region
  alias  = "connectivity-staging"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_connectivity_staging_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = merge(local.default_tags, {
      "pp:environment" = "staging"
    })
  }
}

provider "aws" {
  region = var.region
  alias  = "charge-sessions-prod"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.charge_sessions_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = merge(local.default_tags, {
      "pp:environment" = "production"
    })
  }
}

provider "aws" {
  region = var.region
  alias  = "charge-sessions-staging"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.charge_sessions_staging_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = merge(local.default_tags, {
      "pp:environment" = "staging"
    })
  }
}

provider "aws" {
  region = var.region
  alias  = "pp-network-hub"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pp_network_hub_aws_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = merge(local.default_tags, {
      Project = "Connectivity"
    })
  }
}

provider "aws" {
  region = var.region
  alias  = "vpn"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.vpn_aws_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = var.region
  alias  = "network-assets-prod"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.network_assets_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = merge(local.default_tags, {
      "pp:environment" = "production"
    })
  }
}

provider "aws" {
  region = var.region
  alias  = "cs-state-prod"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_state_prod_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = var.region
  alias  = "cs-state-staging"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_state_staging_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = var.region
  alias  = "cs-state-dev"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_state_dev_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}
