/*
 * Policy to allow access to cs-state APIs
 */
resource "aws_iam_policy" "api_client" {
  provider    = aws.pod-point-eu-west-1
  name        = "${local.name}-api-access-policy-${local.environment}"
  description = "Allow invoke access to cs-state APIs"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "execute-api:Invoke",
        ]
        Resource = [
          format("arn:aws:execute-api:%s:%s:%s/%s/GET/firmware/device",
            local.region,
            data.aws_caller_identity.current.account_id,
            module.firmware_upgrade.api_gateway_id,
            module.firmware_upgrade.api_gateway_stage_name,
          ),
          format("arn:aws:execute-api:%s:%s:%s/%s/GET/firmware/device/*",
            local.region,
            data.aws_caller_identity.current.account_id,
            module.firmware_upgrade.api_gateway_id,
            module.firmware_upgrade.api_gateway_stage_name,
          ),
          format("arn:aws:execute-api:%s:%s:%s/%s/GET/events/*",
            local.region,
            data.aws_caller_identity.current.account_id,
            module.diagnostics.api_gateway_id,
            module.diagnostics.api_gateway_stage_name,
          )
        ]
      },
    ]
  })
}
