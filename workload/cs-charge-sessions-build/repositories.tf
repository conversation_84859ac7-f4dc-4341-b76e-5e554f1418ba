module "ecs_charge_sessions_service" {
  source  = "terraform-enterprise.pod-point.com/technology/ecr/aws"
  version = "1.0.1"

  for_each   = toset(local.charge_sessions_components)
  identifier = format("charge-sessions-service-%s", each.value)

  image_read_access_arns = [
    "arn:aws:iam::${data.aws_caller_identity.current.id}:root",
    "arn:aws:iam::${local.development_account_id}:root",
    "arn:aws:iam::${local.staging_account_id}:root",
    "arn:aws:iam::${local.production_account_id}:root"
  ]

  image_write_access_arns = ["arn:aws:iam::${data.aws_caller_identity.current.id}:root"]
  repo_names              = ["connectivity"]
  image_tag_mutability    = "IMMUTABLE"
}
