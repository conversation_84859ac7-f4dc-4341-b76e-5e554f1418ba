locals {
  environment                  = "staging"
  podadmin_read_endpoint       = "podadmin-staging.cluster-ro-chbvibnzxkke.eu-west-1.rds.amazonaws.com"
  podadmin_read_write_endpoint = "podadmin-staging.cluster-chbvibnzxkke.eu-west-1.rds.amazonaws.com"
  firmware_upgrade_api_url     = "https://34q4zjsftb-vpce-0876136c82b062f17.execute-api.eu-west-1.amazonaws.com/staging"
  region                       = "eu-west-1"
  vpc_cidr_block               = "10.22.0.0/16"
  assume_role_name             = "terraform-ci"
  pod_point_main_account_id    = "************"
  podadmin_security_group_id   = "sg-0ef05fc0f6219f8cd"
  cs_connectivity_account_ids = {
    staging = "************"
    dev     = "************"
  }
  cs_state_account_id                = "************"
  experience_account_ids             = ["************", "************"]
  experience_account_id              = data.aws_ssm_parameter.experience_account_id.value
  salesforce_account_id              = data.aws_ssm_parameter.salesforce_account_id.value
  ownership_data_platform_account_id = data.aws_ssm_parameter.ownership_data_platform_account_id.value
  shared_services_account_id         = "************"
  network_assets_build_account_id    = data.aws_ssm_parameter.network_assets_build_account_id.value

  additional_kms_administrators = data.aws_iam_roles.additional_kms_administrators.arns

  cs_command_responses_topic_arn          = "arn:aws:sns:eu-west-1:${local.cs_connectivity_account_ids.staging}:cs-command-responses"
  ocpp_cs_requests_topic_arn              = "arn:aws:sns:eu-west-1:${local.cs_connectivity_account_ids.staging}:ocpp-cs-requests"
  installation_completed_events_topic_arn = "arn:aws:sns:eu-west-1:${local.experience_account_id}:installation-completed-events"

  default_tags = {
    "pp:domain"                           = "network"
    "pp:environment"                      = "staging"
    "pp:terraformConfigurationRepository" = "pod-point/terraform"
    "pp:terraformWorkspace"               = "network-assets-${local.environment}"
    "pp:owner"                            = "network:assets"
    "pp:service"                          = "asset-service"
  }

  certificate_service_api = {
    region     = "eu-west-1",
    account_id = local.cs_state_account_id,
    api_id     = "4ntzj4s3jb",
    stage      = local.environment,
  }
}
