module "ecommerce_web_app_uk" {
  source = "../../modules/applications/ownership-orders/ecommerce-web-app"

  providers = {
    aws         = aws
    aws.global  = aws.global
    aws.route53 = aws.pod-point-main-global
  }

  access_token                = var.amplify_setup_access_token
  bigcommerce_store_subdomain = "payment-staging"
  bigcommerce_store_hash      = "dr3l2e8dhu"
  branch                      = "staging"
  country                     = "uk"
  domain                      = "pod-point.com"
  enable_basic_auth           = true
  enable_waf                  = false
  environment                 = "staging"
  manage_domain               = true
  subdomain                   = "shop-staging"
  migrate_domain = {
    from   = "d2q1wjgxoya2og.cloudfront.net"
    active = true
  }

  environment_variables = {
    CYPRESS_USER_AUTH_EMAIL             = "<EMAIL>"
    GATSBY_BIG_COMMERCE_PAYMENT_API_URL = "https://payment-staging.pod-point.com"
    GATSBY_BIG_COMMERCE_PROXY_API_URL   = "https://ecommerce-service-staging.pod-point.com"
    GATSBY_BIGCOMMERCE_CHANNEL          = "1"
    GATSBY_BIGCOMMERCE_STORE_HASH       = "dr3l2e8dhu"
    GATSBY_FIREBASE_AUTH_DOMAIN         = "opencharge-mobile-app-stage.firebaseapp.com"
    GATSBY_FIREBASE_PROJECT_ID          = "opencharge-mobile-app-stage"
    GATSBY_GTM_PREVIEW                  = "env-148"
    GATSBY_PRISMIC_LOCALE               = "en-gb"
    GATSBY_PRISMIC_PREVIEW_ENABLED      = "1"
    GATSBY_VWO_ACCOUNT_API_KEY          = "cf6205542467c81d08dc2a1c8c5936e8"
    SYNC_WEBHOOKS                       = "0"
    VEHICLE_ENDPOINT_MODELS             = "https://vehicles-api-staging.pod-point.com/models"
    VEHICLE_ENDPOINT_TOKEN              = "https://pod-point-vehicles-api-staging.auth.eu-west-1.amazoncognito.com/oauth2/token"
  }

  build_notifications_emails = [
    "<EMAIL>",
  ]
}

module "ecommerce_web_app_ie" {
  source = "../../modules/applications/ownership-orders/ecommerce-web-app"

  providers = {
    aws         = aws
    aws.global  = aws.global
    aws.route53 = aws.pod-point-main-global # unsued for pod-point.ie atm
  }

  access_token      = var.amplify_setup_access_token
  branch            = "staging"
  country           = "ie"
  domain            = "pod-point.ie"
  enable_basic_auth = true
  enable_waf        = false
  environment       = "staging"
  manage_domain     = false
  subdomain         = "shop-staging"
  migrate_domain = {
    from   = "d31bah260n691l.cloudfront.net"
    active = true
  }

  environment_variables = {
    CYPRESS_USER_AUTH_EMAIL             = "<EMAIL>"
    GATSBY_BIG_COMMERCE_PAYMENT_API_URL = "https://payment-staging.pod-point.ie"
    GATSBY_BIG_COMMERCE_PROXY_API_URL   = "https://ecommerce-service-staging.pod-point.ie"
    GATSBY_BIGCOMMERCE_CHANNEL          = "1196838"
    GATSBY_BIGCOMMERCE_STORE_HASH       = "dr3l2e8dhu"
    GATSBY_FIREBASE_AUTH_DOMAIN         = "opencharge-mobile-app-stage.firebaseapp.com"
    GATSBY_FIREBASE_PROJECT_ID          = "opencharge-mobile-app-stage"
    GATSBY_GTM_PREVIEW                  = "env-148"
    GATSBY_PRISMIC_LOCALE               = "en-ie"
    GATSBY_PRISMIC_PREVIEW_ENABLED      = "1"
    GATSBY_VWO_ACCOUNT_API_KEY          = "cf6205542467c81d08dc2a1c8c5936e8"
    SYNC_WEBHOOKS                       = "0"
    VEHICLE_ENDPOINT_MODELS             = "https://vehicles-api-staging.pod-point.com/models"
    VEHICLE_ENDPOINT_TOKEN              = "https://pod-point-vehicles-api-staging.auth.eu-west-1.amazoncognito.com/oauth2/token"
  }

  build_notifications_emails = [
    "<EMAIL>",
  ]
}
