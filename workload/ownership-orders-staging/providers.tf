provider "aws" {
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.staging_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-orders-staging"
      "pp:terraformWorkspace"               = "workload/ownership-orders-staging"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  region = "us-east-1"
  alias  = "global"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.staging_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-orders-staging"
      "pp:terraformWorkspace"               = "workload/ownership-orders-staging"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "pod-point-main"
  region = "eu-west-1"

  assume_role {
    role_arn    = format("arn:aws:iam::%s:role/%s", var.pod_point_account_id, var.assume_role_name)
    external_id = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-orders-staging"
      "pp:terraformWorkspace"               = "workload/ownership-orders-staging"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "pod-point-main-global"
  region = "us-east-1"

  assume_role {
    role_arn    = format("arn:aws:iam::%s:role/%s", var.pod_point_account_id, var.assume_role_name)
    external_id = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-orders-staging"
      "pp:terraformWorkspace"               = "workload/ownership-orders-staging"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}
