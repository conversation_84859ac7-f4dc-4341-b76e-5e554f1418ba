# Chatbot IAM role for all chatbot configurations
resource "aws_iam_role" "chatbot_role" {
  name = "chatbot-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "chatbot.amazonaws.com"
        }
      }
    ]
  })
}

# Chatbot IAM Policy
resource "aws_iam_policy" "chatbot_policy" {
  name        = "chatbot-policy"
  description = "Policy for chatbot role"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "cloudwatch:Describe*",
          "cloudwatch:Get*",
          "cloudwatch:List*"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = ["arn:aws:logs:*:*:*"]
      },
      {
        Effect = "Allow",
        Action = [
          "chatbot:CreateSlackChannelConfiguration",
          "chatbot:DeleteSlackChannelConfiguration",
          "chatbot:DescribeSlackChannelConfigurations",
          "chatbot:DescribeSlackChannels",
          "chatbot:DescribeSlackWorkspaces",
          "chatbot:GetSlackOauthParameters",
          "chatbot:RedeemSlackOauthCode",
          "chatbot:UpdateSlackChannelConfiguration"
        ],
        Resource = [
          "*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "sns:Subscribe"
        ],
        Resource = ["arn:aws:sns:*:235716190788:*"]
      }
    ]
  })
}

# Chatbot role policy attachment
resource "aws_iam_role_policy_attachment" "chatbot_policy_attachmnent" {
  policy_arn = aws_iam_policy.chatbot_policy.arn
  role       = aws_iam_role.chatbot_role.name
}

# Policy for all chatbot SNS topics
data "aws_iam_policy_document" "chatbot_sns_topic_policy" {
  policy_id = "chatbot_policy"
  statement {
    actions = [
      "SNS:Publish"
    ]
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }
    resources = [
      "arn:aws:sns:eu-west-1:235716190788:*"
    ]
    sid = "chatbot_policy"
  }
}

# Chatbot and SNS configuration for squad-cloud-platform slack channel
resource "aws_chatbot_slack_channel_configuration" "squad_cloud_platform" {
  configuration_name = "squad-cloud-platform"
  iam_role_arn       = aws_iam_role.chatbot_role.arn
  logging_level      = "ERROR"
  slack_channel_id   = "C04CPGEK674"
  slack_team_id      = "T04AZA4KUHG"
  sns_topic_arns     = [aws_sns_topic.squad_cloud_platform.arn]
}

resource "aws_sns_topic" "squad_cloud_platform" {
  name = "squad-cloud-platform"
}

resource "aws_sns_topic_subscription" "squad_cloud_platform" {
  topic_arn = aws_sns_topic.squad_cloud_platform.arn
  protocol  = "https"
  endpoint  = "https://global.sns-api.chatbot.amazonaws.com"
}

resource "aws_sns_topic_policy" "squad_cloud_platform_policy" {
  arn    = aws_sns_topic.squad_cloud_platform.arn
  policy = data.aws_iam_policy_document.chatbot_sns_topic_policy.json
}

# Chatbot and SNS configuration for domain-network-support slack channel
resource "aws_chatbot_slack_channel_configuration" "domain_network_support" {
  configuration_name = "domain-network-support"
  iam_role_arn       = aws_iam_role.chatbot_role.arn
  logging_level      = "ERROR"
  slack_channel_id   = "C04ESUY76ET"
  slack_team_id      = "T04AZA4KUHG"
  sns_topic_arns     = [aws_sns_topic.domain_network_support.arn]
}

resource "aws_sns_topic" "domain_network_support" {
  name = "domain-network-support"
}

resource "aws_sns_topic_subscription" "domain_network_support" {
  topic_arn = aws_sns_topic.domain_network_support.arn
  protocol  = "https"
  endpoint  = "https://global.sns-api.chatbot.amazonaws.com"
}

resource "aws_sns_topic_policy" "domain_network_support_policy" {
  arn    = aws_sns_topic.domain_network_support.arn
  policy = data.aws_iam_policy_document.chatbot_sns_topic_policy.json
}

# Chatbot and SNS configuration for domain-ownership-support slack channel
resource "aws_chatbot_slack_channel_configuration" "domain_ownership_support" {
  configuration_name = "domain-ownership-support"
  iam_role_arn       = aws_iam_role.chatbot_role.arn
  logging_level      = "ERROR"
  slack_channel_id   = "C055VVAKVRC"
  slack_team_id      = "T04AZA4KUHG"
  sns_topic_arns     = [aws_sns_topic.domain_ownership_support.arn]
}

resource "aws_sns_topic" "domain_ownership_support" {
  name = "domain-ownership-support"
}

resource "aws_sns_topic_subscription" "domain_ownership_support" {
  topic_arn = aws_sns_topic.domain_ownership_support.arn
  protocol  = "https"
  endpoint  = "https://global.sns-api.chatbot.amazonaws.com"
}

resource "aws_sns_topic_policy" "domain_ownership_support_policy" {
  arn    = aws_sns_topic.domain_ownership_support.arn
  policy = data.aws_iam_policy_document.chatbot_sns_topic_policy.json
}

# Chatbot and SNS configuration for domain-experience-support slack channel
resource "aws_chatbot_slack_channel_configuration" "domain_experience_support" {
  configuration_name = "domain-experience-support"
  iam_role_arn       = aws_iam_role.chatbot_role.arn
  logging_level      = "ERROR"
  slack_channel_id   = "C04D2C1D7N3"
  slack_team_id      = "T04AZA4KUHG"
  sns_topic_arns     = [aws_sns_topic.domain_experience_support.arn]
}

resource "aws_sns_topic" "domain_experience_support" {
  name = "domain-experience-support"
}

resource "aws_sns_topic_subscription" "domain_experience_support" {
  topic_arn = aws_sns_topic.domain_experience_support.arn
  protocol  = "https"
  endpoint  = "https://global.sns-api.chatbot.amazonaws.com"
}

resource "aws_sns_topic_policy" "domain_experience_support_policy" {
  arn    = aws_sns_topic.domain_experience_support.arn
  policy = data.aws_iam_policy_document.chatbot_sns_topic_policy.json
}

# Chatbot and SNS configuration for squad-internal-systems slack channel
resource "aws_chatbot_slack_channel_configuration" "squad_internal_systems" {
  configuration_name = "squad-internal-systems"
  iam_role_arn       = aws_iam_role.chatbot_role.arn
  logging_level      = "ERROR"
  slack_channel_id   = "C04MHCQ4RC3"
  slack_team_id      = "T04AZA4KUHG"
  sns_topic_arns     = [aws_sns_topic.squad_internal_systems.arn]
}

resource "aws_sns_topic" "squad_internal_systems" {
  name = "squad-internal-systems"
}

resource "aws_sns_topic_subscription" "squad_internal_systems" {
  topic_arn = aws_sns_topic.squad_internal_systems.arn
  protocol  = "https"
  endpoint  = "https://global.sns-api.chatbot.amazonaws.com"
}

resource "aws_sns_topic_policy" "squad_internal_systems_policy" {
  arn    = aws_sns_topic.squad_internal_systems.arn
  policy = data.aws_iam_policy_document.chatbot_sns_topic_policy.json
}