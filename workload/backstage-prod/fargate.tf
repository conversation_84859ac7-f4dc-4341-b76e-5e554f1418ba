data "aws_caller_identity" "current" {}

locals {
  identifier  = "backstage"
  environment = "prod"

  ecs_service_name           = "backstage-app"
  ecs_backstage_backend_port = "7007"
}

data "terraform_remote_state" "backstage_build" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "backstage-build"
    }
  }
}

module "ecs_common_resources" {
  source = "../../modules/ecs/common"
}

module "ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/pod-point/fargate-service/aws//modules/cluster"
  version = "10.1.1"

  identifier         = local.identifier
  repo_names         = ["backstage-podpoint"]
  container_insights = "enabled"

  depends_on = [
    module.ecs_common_resources
  ]
}

module "ecs_service" {
  source  = "terraform-enterprise.pod-point.com/pod-point/fargate-service/aws"
  version = "10.1.1"

  service_type                   = "rolling"
  identifier                     = local.ecs_service_name
  vpc_id                         = module.vpc.vpc_id
  cluster_name                   = module.ecs_cluster.name
  cluster_arn                    = module.ecs_cluster.arn
  pipeline_role_name             = module.ecs_cluster.github_role_name
  cpu                            = "256"
  memory                         = "512"
  capacity_fargate_base          = 1
  capacity_fargate_weight        = 1
  capacity_fargate_spot_base     = 0
  capacity_fargate_spot_weight   = 0
  subnet_ids                     = module.vpc.private_subnets_ids
  enable_auto_scaling            = false
  scaling_metric                 = "CPUUtilization"
  scaling_metric_threshold       = 80
  scaling_min_capacity           = 2
  scaling_max_capacity           = 5
  scale_out_cooldown             = 120
  scale_out_gradual_adjustment   = 1
  scale_out_gradual_lower_bound  = 0
  scale_out_gradual_upper_bound  = 20
  scale_out_critical_adjustment  = 3
  scale_out_critical_lower_bound = 20
  scale_in_cooldown              = 120
  scale_in_upper_bound           = 0
  scale_in_adjustment            = -5

  load_balancing_configuration = [
    {
      target_group_arn = module.alb.target_group_arns[0]
      container_name   = local.ecs_service_name
      container_port   = local.ecs_backstage_backend_port
    }
  ]

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  container_definitions = jsonencode([
    {
      "name" : local.ecs_service_name,
      "image" : "${data.terraform_remote_state.backstage_build.outputs.account_id}.dkr.ecr.eu-west-1.amazonaws.com/${local.identifier}:sha-c11efbb",
      "essential" : true,
      "networkMode" : "awsvpc",
      "readonly_root_filesystem" : false,
      "portMappings" : [
        {
          "protocol" : "tcp",
          "containerPort" : tonumber(local.ecs_backstage_backend_port),
          "hostPort" : tonumber(local.ecs_backstage_backend_port)
        }
      ],
      "linuxParameters" : {
        "initProcessEnabled" : true
      },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${local.ecs_service_name}",
          "awslogs-region" : "eu-west-1",
          "awslogs-stream-prefix" : "ecs"
        }
      }
      "environment" : [
        {
          "name" : "GITHUB_APP_PARAM_NAME",
          "value" : aws_ssm_parameter.github_app_backstage_credentials.name
        }
      ],
      "secrets" : [
        {
          "name" : "GITHUB_CLIENT_ID",
          "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.github_app_auth_credentials.arn, "clientId")
        },
        {
          "name" : "GITHUB_CLIENT_SECRET",
          "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.github_app_auth_credentials.arn, "clientSecret")
        },
        {
          "name" : "APP_BASE_URL",
          "valueFrom" : aws_ssm_parameter.backstage_app_base_url.arn
        },
        {
          "name" : "POSTGRES_HOST",
          "valueFrom" : module.rds.common_ssm_parameters.ssm_parameter_database_address
        },
        {
          "name" : "POSTGRES_PORT",
          "valueFrom" : module.rds.common_ssm_parameters.ssm_parameter_database_port
        },
        {
          "name" : "POSTGRES_USER",
          "valueFrom" : module.rds.common_ssm_parameters.ssm_parameter_database_username
        },
        {
          "name" : "POSTGRES_PASSWORD",
          "valueFrom" : format("%s:%s::", module.rds.admin_user_secret_manager_arn, "password")
        },
        {
          "name" : "GRAFANA_TOKEN",
          "valueFrom" : aws_secretsmanager_secret.grafana_token.arn
        },
        {
          "name" : "OPSGENIE_TOKEN",
          "valueFrom" : aws_secretsmanager_secret.opsgenie_token.arn
        },
        {
          "name" : "SENTRY_TOKEN",
          "valueFrom" : aws_secretsmanager_secret.sentry_token.arn
        }
      ]
    }
  ])

  depends_on = [
    module.ecs_common_resources
  ]
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *        Custom IAM Policy
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

data "aws_iam_policy_document" "custom_ecs_task_execution_policy" {
  statement {
    sid = "RetrieveSSMParams"

    actions = ["ssm:GetParameters"]

    resources = [
      aws_ssm_parameter.backstage_app_base_url.arn,
      module.rds.common_ssm_parameters.ssm_parameter_database_address,
      module.rds.common_ssm_parameters.ssm_parameter_database_port,
      module.rds.common_ssm_parameters.ssm_parameter_database_username
    ]
  }

  statement {
    sid = "RetrieveSecretManagerSecretValues"

    actions = ["secretsmanager:GetSecretValue"]

    resources = [
      module.rds.admin_user_secret_manager_arn,
      aws_secretsmanager_secret.github_app_auth_credentials.arn,
      aws_secretsmanager_secret.grafana_token.arn,
      aws_secretsmanager_secret.opsgenie_token.arn,
      aws_secretsmanager_secret.sentry_token.arn
    ]
  }

  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]
    resources = [
      aws_kms_key.backend.arn
    ]
  }
}

data "aws_iam_policy_document" "custom_ecs_task_container_policy" {
  statement {
    sid = "RetrieveSSMParams"

    actions = [
      "ssm:GetParameters",
      "ssm:GetParameter"
    ]

    resources = [
      aws_ssm_parameter.github_app_backstage_credentials.arn
    ]
  }

  statement {
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
    effect    = "Allow"
  }

  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]
    resources = [
      aws_kms_key.backend.arn
    ]
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *    Fargate Security Group Rules
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_security_group_rule" "ecs_fargate_allow_backend_port_from_loadbalancer" {
  type                     = "ingress"
  from_port                = local.ecs_backstage_backend_port
  to_port                  = local.ecs_backstage_backend_port
  protocol                 = "tcp"
  source_security_group_id = module.alb.security_group_id
  security_group_id        = module.ecs_service.security_group_id
}

resource "aws_security_group_rule" "ecs_fargate_allow_all_egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = module.ecs_service.security_group_id
}
