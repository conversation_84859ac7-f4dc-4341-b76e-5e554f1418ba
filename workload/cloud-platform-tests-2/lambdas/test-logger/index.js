const FluentClient = require('@fluent-org/logger').FluentClient;

exports.handler = async function () {

    const logger = new FluentClient('lambda.test', {
        socket: {
          host: process.env.FLUENTD_HOST,
          port: 24224,
          timeout: 3000, // 3 seconds
        }
    });

    let timestampMs
    let timestampNs
    const severity_text = "INFO"
    const severity_number = 13
    const body = "this is a log for a lambda"
    const resource = {
        namespace: "my_domain",
        name: "my_app_2",
        version: "v1.0.4"
    }
    const attributes = [
        {
            key: "lambda_name",
            value: process.env.AWS_LAMBDA_FUNCTION_NAME // Predefined lambda environment variable, see: https://docs.aws.amazon.com/lambda/latest/dg/configuration-envvars.html#configuration-envvars-runtime
        }
    ]

    const log = {
        timeUnixNano: 0,
        severityText: severity_text,
        severityNumber: severity_number,
        body: body,
        resource: resource,
        attributes: attributes
    }

    for (let i = 0; i < 10; i++) {
        timestampMs = Date.now()
        timestampNs = Number(timestampMs * 1000000) // Converting ms from Date.now() to ns just for the POC, not the same as getting current epoch time in nanoseconds
        log["timeUnixNano"] = timestampNs
        console.log(log)
        await logger.emit(log);
    }
};