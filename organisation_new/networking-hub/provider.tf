provider "aws" {
  region = var.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.assume_role_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "pod-point"
  region = var.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pod_point_acc_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "pp-data-dev"
  region = var.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pp_data_dev_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "development"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "pp-data-staging"
  region = var.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pp_data_staging_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "pp-data-prod"
  region = var.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pp_data_prod_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "pp-sandbox"
  region = var.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pp_sandbox_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "sandbox"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "experience-dev"
  region = var.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.experience_dev_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "development"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "experience-staging"
  region = var.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.experience_staging_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "experience-prod"
  region = var.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.experience_prod_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "cs-state-dev"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_state_dev_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment" = "development"

      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "cs-state-staging"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_state_staging_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "cs-state-prod"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_state_prod_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "terraform-enterprise"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.terraform_enterprise_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "cs-connectivity-dev"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_connectivity_dev_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "development"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "cs-connectivity-staging"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_connectivity_staging_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "cs-connectivity-prod"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_connectivity_prod_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "cs-charge-sessions-dev"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_charge_sessions_dev_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "development"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "cs-charge-sessions-staging"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_charge_sessions_staging_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "cs-charge-sessions-prod"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.cs_charge_sessions_prod_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "network-assets-dev"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.network_assets_dev_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "development"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "network-assets-staging"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.network_assets_staging_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "network-assets-prod"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.network_assets_prod_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "ownership-data-platform-dev"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.ownership_data_platform_dev_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "development"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "ownership-data-platform-staging"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.ownership_data_platform_staging_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "ownership-data-platform-prod"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.ownership_data_platform_prod_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "vpn"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.vpn_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "ownership-acquisition-dev"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.ownership_acquisition_dev_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "development"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "ownership-acquisition-staging"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.ownership_acquisition_staging_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "ownership-acquisition-prod"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.ownership_acquisition_prod_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "ownership-orders-dev"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.ownership_orders_dev_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "dev"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "ownership-orders-staging"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.ownership_orders_staging_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "ownership-orders-prod"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.ownership_orders_prod_acc_id, "terraform-ci")
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "prod"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "networking-hub"
      "pp:terraformWorkspace"               = "pod-point/networking-hub"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

