{"rules": [{"rule-type": "selection", "rule-id": "1", "rule-name": "1", "object-locator": {"schema-name": "podpoint", "table-name": "pod_unit_user"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "2", "rule-name": "2", "object-locator": {"schema-name": "podpoint", "table-name": "pod_connectors"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "3", "rule-name": "3", "object-locator": {"schema-name": "podpoint", "table-name": "pod_locations"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "4", "rule-name": "4", "object-locator": {"schema-name": "podpoint", "table-name": "group_location"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "5", "rule-name": "5", "object-locator": {"schema-name": "podpoint", "table-name": "groups"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "6", "rule-name": "6", "object-locator": {"schema-name": "podpoint", "table-name": "pod_location_unit"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "7", "rule-name": "7", "object-locator": {"schema-name": "podpoint", "table-name": "pod_units"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "8", "rule-name": "8", "object-locator": {"schema-name": "podpoint", "table-name": "pod_models"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "9", "rule-name": "9", "object-locator": {"schema-name": "podpoint", "table-name": "pod_ranges"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "10", "rule-name": "10", "object-locator": {"schema-name": "podpoint", "table-name": "pcb_pod_unit"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "11", "rule-name": "11", "object-locator": {"schema-name": "podpoint", "table-name": "pcbs"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "12", "rule-name": "12", "object-locator": {"schema-name": "podpoint", "table-name": "pcb_commands"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "13", "rule-name": "13", "object-locator": {"schema-name": "podpoint", "table-name": "pcb_status_history"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "14", "rule-name": "14", "object-locator": {"schema-name": "podpoint", "table-name": "pcb_software_version"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "15", "rule-name": "15", "object-locator": {"schema-name": "podpoint", "table-name": "software_versions"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "16", "rule-name": "16", "object-locator": {"schema-name": "podpoint", "table-name": "software_version_modules"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "17", "rule-name": "17", "object-locator": {"schema-name": "podpoint", "table-name": "pod_addresses"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "18", "rule-name": "18", "object-locator": {"schema-name": "podpoint", "table-name": "billing_events"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "19", "rule-name": "19", "object-locator": {"schema-name": "podpoint", "table-name": "charges"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "20", "rule-name": "20", "object-locator": {"schema-name": "podpoint", "table-name": "users"}, "rule-action": "include"}, {"rule-type": "transformation", "rule-id": "21", "rule-name": "21", "rule-action": "remove-column", "rule-target": "column", "object-locator": {"schema-name": "podpoint", "table-name": "users", "column-name": "password"}}, {"rule-type": "selection", "rule-id": "22", "rule-name": "22", "object-locator": {"schema-name": "podpoint", "table-name": "ev_drivers"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "23", "rule-name": "23", "object-locator": {"schema-name": "podpoint", "table-name": "members"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "24", "rule-name": "24", "object-locator": {"schema-name": "podpoint", "table-name": "ev_driver_domains"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "25", "rule-name": "25", "object-locator": {"schema-name": "podpoint", "table-name": "pod_address_types"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "26", "rule-name": "26", "object-locator": {"schema-name": "podpoint", "table-name": "charge_methods"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "27", "rule-name": "27", "object-locator": {"schema-name": "podpoint", "table-name": "pod_unit_connector"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "28", "rule-name": "28", "object-locator": {"schema-name": "podpoint", "table-name": "routers"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "29", "rule-name": "29", "object-locator": {"schema-name": "podpoint", "table-name": "router_pod_units"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "30", "rule-name": "30", "object-locator": {"schema-name": "podpoint", "table-name": "tariffs"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "31", "rule-name": "31", "object-locator": {"schema-name": "podpoint", "table-name": "tariff_tiers"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "32", "rule-name": "32", "object-locator": {"schema-name": "podpoint", "table-name": "energy_suppliers"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "33", "rule-name": "33", "object-locator": {"schema-name": "podpoint", "table-name": "revenue_profiles"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "34", "rule-name": "34", "object-locator": {"schema-name": "podpoint", "table-name": "revenue_profile_tiers"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "35", "rule-name": "35", "object-locator": {"schema-name": "podpoint", "table-name": "billing_accounts"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "36", "rule-name": "36", "object-locator": {"schema-name": "podpoint", "table-name": "sockets"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "37", "rule-name": "37", "object-locator": {"schema-name": "podpoint", "table-name": "vehicle_models"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "38", "rule-name": "38", "object-locator": {"schema-name": "podpoint", "table-name": "vehicle_model_user"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "39", "rule-name": "39", "object-locator": {"schema-name": "podpoint", "table-name": "vehicle_makes"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "40", "rule-name": "40", "object-locator": {"schema-name": "podpoint", "table-name": "vehicle_model_countries"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "41", "rule-name": "41", "object-locator": {"schema-name": "podpoint", "table-name": "charge_methods"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "42", "rule-name": "42", "object-locator": {"schema-name": "podpoint", "table-name": "sims"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "43", "rule-name": "43", "object-locator": {"schema-name": "podpoint", "table-name": "claimed_charges"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "44", "rule-name": "44", "object-locator": {"schema-name": "podpoint", "table-name": "authorisers"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "45", "rule-name": "45", "object-locator": {"schema-name": "podpoint", "table-name": "pcb_types"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "46", "rule-name": "46", "object-locator": {"schema-name": "podpoint", "table-name": "pod_unit_charge_schedules"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "47", "rule-name": "47", "object-locator": {"schema-name": "podpoint", "table-name": "charge_schedules"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "48", "rule-name": "48", "object-locator": {"schema-name": "podpoint", "table-name": "pod_components"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "49", "rule-name": "49", "object-locator": {"schema-name": "podpoint", "table-name": "pod_component_range"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "50", "rule-name": "50", "object-locator": {"schema-name": "podpoint", "table-name": "pcb_configurations"}, "rule-action": "include"}, {"rule-type": "table-settings", "rule-id": "51", "rule-name": "51", "object-locator": {"schema-name": "podpoint", "table-name": "charges"}, "parallel-load": {"type": "ranges", "columns": ["starts_at"], "boundaries": [["2012-01-01"], ["2014-01-01"], ["2016-01-01"], ["2018-01-01"], ["2020-01-01"], ["2021-01-01"], ["2022-01-01"], ["2023-01-01"]]}}, {"rule-type": "selection", "rule-id": "52", "rule-name": "52", "object-locator": {"schema-name": "podpoint", "table-name": "pod_vendors"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "53", "rule-name": "53", "object-locator": {"schema-name": "podpoint", "table-name": "charge_schedule_statuses"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "54", "rule-name": "54", "object-locator": {"schema-name": "podpoint", "table-name": "charge_states"}, "rule-action": "include"}, {"rule-type": "table-settings", "rule-id": "55", "rule-name": "55", "object-locator": {"schema-name": "podpoint", "table-name": "charge_states"}, "parallel-load": {"type": "ranges", "columns": ["starts_at"], "boundaries": [["2012-01-01"], ["2014-01-01"], ["2016-01-01"], ["2018-01-01"], ["2020-01-01"], ["2021-01-01"], ["2022-01-01"], ["2023-01-01"]]}}, {"rule-type": "selection", "rule-id": "56", "rule-name": "56", "object-locator": {"schema-name": "podpoint", "table-name": "group_types"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "57", "rule-name": "57", "object-locator": {"schema-name": "podpoint", "table-name": "labels"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "58", "rule-name": "58", "object-locator": {"schema-name": "podpoint", "table-name": "label_pod_location"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "59", "rule-name": "59", "object-locator": {"schema-name": "podpoint", "table-name": "label_pod_unit"}, "rule-action": "include"}]}