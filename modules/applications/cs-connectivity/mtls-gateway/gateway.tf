resource "aws_security_group_rule" "vpc_endpoint_provisioning_api_gateway" {
  description              = format("Allow %s to provisioning api", local.identifier)
  security_group_id        = var.vpc_endpoint_provisioning_api_security_group_id
  source_security_group_id = module.gateway.security_group_id
  from_port                = 80
  to_port                  = 80
  protocol                 = "TCP"
  type                     = "ingress"
}

data "aws_iam_policy_document" "task_execution_gateway" {
  override_policy_documents = [data.aws_iam_policy_document.task_execution_common.json]
}

data "aws_iam_policy_document" "task_role_gateway" {
  statement {
    sid     = "PermitAccessToCertificateServiceApi"
    effect  = "Allow"
    actions = ["execute-api:Invoke"]
    resources = [
      format(
        "arn:aws:execute-api:%s:%s:%s/*",
        var.certificate_service_api.region,
        var.certificate_service_api.account_id,
        var.certificate_service_api.api_id
      )
    ]
  }
}

locals {
  gateway_fargate_security_group_egress_rules = {
    "ports_all_open" = {
      description = "Permit all egress traffic."
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }
  gateway_fargate_security_group_ingress_rules = {
    "port_443_nlb" = {
      description              = "Access permitted from loadbalancer."
      from_port                = 443
      to_port                  = 443
      protocol                 = "TCP"
      source_security_group_id = aws_security_group.gateway_nlb.id
    }
  }
}

resource "aws_security_group_rule" "gateway_fargate_egress_rules" {
  for_each = local.gateway_fargate_security_group_egress_rules

  security_group_id = module.gateway.security_group_id

  type             = "egress"
  from_port        = each.value.from_port
  to_port          = each.value.to_port
  protocol         = each.value.protocol
  description      = each.value.description
  cidr_blocks      = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks = lookup(each.value, "ipv6_cidrs", null)
}

resource "aws_security_group_rule" "gateway_fargate_ingress_rules" {
  for_each = local.gateway_fargate_security_group_ingress_rules

  security_group_id = module.gateway.security_group_id

  type                     = "ingress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  source_security_group_id = each.value.source_security_group_id
}

module "gateway" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "13.0.0"

  service_type       = "rolling"
  identifier         = local.identifier
  cluster_name       = module.ecs_cluster.name
  cluster_arn        = module.ecs_cluster.arn
  pipeline_role_name = module.ecs_cluster.github_role_name

  attach_custom_ecs_task_iam_policy = true
  ecs_task_custom_policy            = data.aws_iam_policy_document.task_role_gateway.json

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.task_execution_gateway.json

  memory = try(var.components_fargate_task_size["gateway"]["memory"], var.memory)
  cpu    = try(var.components_fargate_task_size["gateway"]["cpu"], var.cpu)

  capacity_fargate_base        = var.capacity_fargate_base
  capacity_fargate_weight      = var.capacity_fargate_weight
  capacity_fargate_spot_base   = var.capacity_fargate_spot_base
  capacity_fargate_spot_weight = var.capacity_fargate_spot_weight
  container_definitions        = jsonencode([local.gateway_container_definition])

  vpc_id     = var.vpc_id
  subnet_ids = var.vpc_private_subnet_ids

  scaling_min_capacity = 0
  enable_auto_scaling  = false

  log_group_name         = local.gateway_container_definition["logConfiguration"]["options"]["awslogs-group"]
  logs_retention_in_days = try(var.components_log_retention_in_days["gateway"], var.logs_retention_in_days)

  depends_on = [
    module.ecs_cluster,
  ]

  additional_kms_administrators = var.kms_admins

  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"
      ]
    }]
  }]

  load_balancing_configuration = [
    {
      target_group_arn = aws_lb_target_group.gateway.arn
      container_name   = local.gateway_container_definition["name"]
      container_port   = 443
    }
  ]

  tags = local.tags
}

/*
* Network load balancer
*/
resource "aws_security_group" "gateway_nlb" {
  name        = format("%s-nlb", local.identifier)
  description = format("Security group for the Network Load Balancer of %s", local.identifier)
  vpc_id      = var.vpc_id

  tags = local.tags
}

// Allow any TCP traffic on port 443 to the NLB
resource "aws_security_group_rule" "gateway_nlb_ingress_rules" {
  security_group_id = aws_security_group.gateway_nlb.id

  from_port = 443
  to_port   = 443

  protocol = "TCP"
  type     = "ingress"

  cidr_blocks      = ["0.0.0.0/0"]
  ipv6_cidr_blocks = ["::/0"]
}

// Allow any TCP traffic to fargate
resource "aws_security_group_rule" "gateway_nlb_egress_rules" {
  security_group_id = aws_security_group.gateway_nlb.id

  from_port = 443
  to_port   = 443

  protocol = "TCP"
  type     = "egress"

  source_security_group_id = module.gateway.security_group_id
}

resource "aws_lb" "gateway" {
  name               = format("%s-lb", local.identifier)
  internal           = false
  load_balancer_type = "network"
  subnets            = var.vpc_public_subnet_ids

  enable_cross_zone_load_balancing = true
  enable_deletion_protection       = true

  security_groups = [aws_security_group.gateway_nlb.id]

  tags = local.tags
}

resource "aws_lb_target_group" "gateway" {
  name                 = format("%s-tg", local.identifier)
  protocol             = "TCP"
  port                 = 443
  target_type          = "ip"
  vpc_id               = var.vpc_id
  deregistration_delay = 60
  preserve_client_ip   = true

  health_check {
    protocol            = "TCP"
    healthy_threshold   = 2
    unhealthy_threshold = 2
  }

  tags = local.tags
}

resource "aws_lb_listener" "gateway" {
  load_balancer_arn = aws_lb.gateway.arn
  port              = 443
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.gateway.arn
  }

  tags = local.tags
}

resource "aws_security_group_rule" "vpc_endpoint_execute_api_gateway" {
  description              = format("Allow %s to issue certificate", local.identifier)
  security_group_id        = var.vpc_endpoint_execute_api_security_group_id
  source_security_group_id = module.gateway.security_group_id
  from_port                = 443
  to_port                  = 443
  protocol                 = "TCP"
  type                     = "ingress"
}
