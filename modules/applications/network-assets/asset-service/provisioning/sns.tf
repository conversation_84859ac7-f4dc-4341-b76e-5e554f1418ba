resource "aws_sns_topic" "pcb_provisioning_events" {
  name = "pcb-provisioning-events"
}

resource "aws_sns_topic_policy" "pcb_provisioning_events_policy" {
  arn    = aws_sns_topic.pcb_provisioning_events.arn
  policy = data.aws_iam_policy_document.pcb_provisioning_events_policy_document.json
}

data "aws_iam_policy_document" "pcb_provisioning_events_policy_document" {
  statement {
    sid = "AllowProvisioningAPISNSPublishPermission"
    actions = [
      "SNS:Publish",
    ]
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = [module.api.task_role_arn]
    }

    resources = [aws_sns_topic.pcb_provisioning_events.arn]
  }
}

resource "aws_sns_topic" "charging_station_provisioning_events" {
  name = "charging-station-provisioning-events"
}

resource "aws_sns_topic_policy" "charging_station_provisioning_events_policy" {
  arn    = aws_sns_topic.charging_station_provisioning_events.arn
  policy = data.aws_iam_policy_document.charging_station_provisioning_events_policy_document.json
}

data "aws_iam_policy_document" "charging_station_provisioning_events_policy_document" {
  statement {
    sid = "AllowProvisioningAPISNSPublishPermission"
    actions = [
      "SNS:Publish",
    ]
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = [module.api.task_role_arn]
    }

    resources = [aws_sns_topic.charging_station_provisioning_events.arn]
  }

  statement {
    sid = "AllowSNSSubscribeForPodPointAccount"
    actions = [
      "SNS:Subscribe",
    ]
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = var.charging_station_provisioning_events_client_account_ids
    }
    resources = [
      aws_sns_topic.charging_station_provisioning_events.arn,
    ]
  }
}
