data "aws_iam_policy_document" "custom_ecs_task_execution_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend.arn]
  }
  statement {
    sid     = "RetrieveSecretManagerSecretValues"
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.api.arn,
      aws_secretsmanager_secret.webapp.arn,
    ]
  }
  statement {
    sid     = "AllowCreateLogGroup"
    actions = ["logs:CreateLogGroup"]
    resources = [
      "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/ecs/${format("%s-api", local.identifier)}/ecs-aws-otel-sidecar-collector:log-stream:",
      "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/ecs/${format("%s-webapp", local.identifier)}/ecs-aws-otel-sidecar-collector:log-stream:"
    ]
  }
}

data "aws_iam_policy_document" "webapp_ecs_task_container_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend.arn]
  }
  statement {
    sid = "AllowSessionManager"
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
  }
  statement {
    sid = "AllowXray"
    actions = [
      "xray:PutTraceSegments",
      "xray:PutTelemetryRecords",
      "xray:GetSamplingRules",
      "xray:GetSamplingTargets",
      "xray:GetSamplingStatisticSummaries"
    ]
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "api_ecs_task_container_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend.arn]
  }
  statement {
    sid = "AllowSessionManager"
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
  }
  statement {
    sid       = "AllowAPIInvoke"
    actions   = ["execute-api:Invoke"]
    resources = ["*"]
  }
  statement {
    sid = "AllowXray"
    actions = [
      "xray:PutTraceSegments",
      "xray:PutTelemetryRecords",
      "xray:GetSamplingRules",
      "xray:GetSamplingTargets",
      "xray:GetSamplingStatisticSummaries"
    ]
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "backend" {
  statement {
    sid       = "EnableAccountAdministration"
    actions   = ["kms:*"]
    resources = ["*"]

    principals {
      type        = "AWS"
      identifiers = concat([format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id)])
    }
  }

  statement {
    sid       = "AllowKMSDecrypt"
    actions   = ["kms:Decrypt"]
    resources = ["*"]

    principals {
      type = "AWS"
      identifiers = [
        module.api.execution_role_arn,
        module.api.task_role_arn,
        module.webapp.execution_role_arn,
        module.webapp.task_role_arn,
      ]
    }
  }

  statement {
    sid = "AllowKMSDecryptAdminBreakGlass"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    condition {
      test     = "StringLike"
      variable = "aws:PrincipalArn"
      values   = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"]
    }
  }
}

resource "aws_iam_role_policy_attachment" "webapp_role_policy_attachments" {
  for_each = toset([
    "AmazonSSMManagedInstanceCore",
    "CloudWatchAgentServerPolicy"
  ])
  role       = module.webapp.task_role_name
  policy_arn = format("arn:aws:iam::aws:policy/%s", each.key)
}
