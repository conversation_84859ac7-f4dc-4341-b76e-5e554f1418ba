locals {
  identifier = "onboarding-service"

  pod_point_com_domain_name = "pod-point.com"
  pod_point_com_hosted_zone = "ZI1YF8KE9MFAW"
  podenergy_com_domain_name = "podenergy.com"
  podenergy_com_hosted_zone = "Z0275178UFD5TRC1H8T"

  webapp_container_port = 8101

  tags = merge(data.aws_default_tags.current.tags, {
    "pp:owner"   = "experience:workplace-public"
    "pp:service" = local.identifier
  })
}
