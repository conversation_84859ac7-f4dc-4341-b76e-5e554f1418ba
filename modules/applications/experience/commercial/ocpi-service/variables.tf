variable "admin_user_secret_manager_arn" {
  description = "ARN of the secret containing the admin password for the RDS cluster to be used by the service."
  type        = string
}

variable "alb_public_subnets" {
  description = "The public subnets to assign to the ALB."
  type        = list(string)
}

variable "api_database_cluster_resource_id" {
  description = "Cluster resource id for the RDS cluster to be used by the service."
  type        = string
}

variable "api_desired_task_count" {
  description = "API desired task count."
  type        = number
}

variable "cloudfront_enable_logging" {
  description = "Whether to enable logging for the cloudfront distribution."
  type        = bool
  default     = false
}

variable "cloudfront_realtime_metrics" {
  description = "Whether to realtime metrics for the cloudfront distribution."
  type        = string
  default     = "Disabled"
}

variable "codebuild_security_group_ids" {
  description = "Security group ids of codebuild for post deploy testing."
  type        = list(string)
}

variable "charger_status_updates_account_id" {
  description = "Account ID that corresponds to SNS topic being subscribed to for charger status updates."
  type        = string
}

variable "destination_cluster_endpoint" {
  description = "The DNS address of the RDS instance to be used by the service."
  type        = string
}

variable "destination_cluster_port" {
  description = "The port used by the RDS cluster to be used by the service."
  type        = string
}

variable "destination_reader_endpoint" {
  description = "The DNS address of the RDS replica instance to be used by the service."
  type        = string
}

variable "ecs_private_subnets_ids" {
  description = "The public subnets to be used by ECS services and tasks."
  type        = list(string)
}

variable "enable_scheduled_tasks" {
  description = "Whether or not to enable scheduled tasks."
  type        = bool
}

variable "environment" {
  type        = string
  description = "The name of the environment. This will be used when it is necessary to namespace resources (e.g. S3 buckets)."
}

variable "experience_cluster_security_group_id" {
  description = "Security group id for the RDS cluster to be used by the service."
  type        = string
}

variable "podadmin_host" {
  description = "The hostname for the podadmin database."
  type        = string
}

variable "podadmin_host_ro" {
  description = "The hostname for the podadmin database (read-only)."
  type        = string
}

variable "privatelink_client_service_names" {
  description = "The VPC endpoint service names used to connect to load balancers in other AWS Account VPCs."
  type = object({
    connectivity_commands_api = string
    connectivity_status_api   = string
  })
}

variable "queue_worker_desired_task_count" {
  description = "Queue worker desired task count."
  type        = number
}

variable "route53_record_name" {
  type        = string
  description = "Route 53 record name for this service."
  default     = null
}

variable "podenergy_route53_record_name" {
  type        = string
  description = "Route 53 record name for this service on the podenergy.com domain."
  default     = null
}

variable "use_spot_capacity" {
  description = "Whether or not to use spot capacity."
  type        = bool
}

variable "vpc_id" {
  description = "The ID of the VPC to be used by the service."
  type        = string
}
