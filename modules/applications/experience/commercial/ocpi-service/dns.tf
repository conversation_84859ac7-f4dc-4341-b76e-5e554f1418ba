# DNS record for pod-point.com domain
resource "aws_route53_record" "this" {
  provider = aws.route53

  zone_id         = local.pod_point_com_hosted_zone
  name            = var.route53_record_name
  type            = "A"
  allow_overwrite = false

  alias {
    name                   = aws_cloudfront_distribution.this.domain_name
    zone_id                = aws_cloudfront_distribution.this.hosted_zone_id
    evaluate_target_health = true
  }
}

# DNS record for podenergy.com domain
resource "aws_route53_record" "podenergy" {
  count    = var.podenergy_route53_record_name != null ? 1 : 0
  provider = aws.route53

  zone_id         = local.podenergy_com_hosted_zone
  name            = var.podenergy_route53_record_name
  type            = "A"
  allow_overwrite = false

  alias {
    name                   = aws_cloudfront_distribution.podenergy[0].domain_name
    zone_id                = aws_cloudfront_distribution.podenergy[0].hosted_zone_id
    evaluate_target_health = true
  }
}
