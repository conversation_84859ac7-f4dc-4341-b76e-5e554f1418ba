module "alb" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "2.2.1"
  tags    = local.tags

  load_balancer_name = local.identifier
  enable_internal_lb = false
  load_balancer_type = "application"

  vpc_id                = var.vpc_id
  load_balancer_subnets = var.alb_public_subnets

  access_logs = {
    enable_logs          = true
    create_bucket_policy = true
  }

  target_groups = [
    {
      name                 = local.identifier
      backend_protocol     = "HTTP"
      backend_port         = local.api_container_port
      target_type          = "ip"
      deregistration_delay = 300
      slow_start           = "0"
      vpc_id               = var.vpc_id

      health_check = {
        enabled             = true
        interval            = 30
        path                = "/health"
        port                = "traffic-port"
        healthy_threshold   = 3
        unhealthy_threshold = 2
        timeout             = 10
        protocol            = "HTTP"
        matcher             = "200-299"
      }
    }
  ]

  https_listener_rules = concat([
    {
      https_listener_index = 0
      priority             = 1
      actions = [
        {
          type               = "forward"
          target_group_index = 0
        }
      ]

      conditions = [{
        http_headers = [{
          http_header_name = "X-CF-Auth-Secret"
          values           = [random_string.random_cf_header_val.result]
        }]
      }]
    }
  ], var.podenergy_route53_record_name != null ? [
    {
      https_listener_index = 0
      priority             = 2
      actions = [
        {
          type               = "forward"
          target_group_index = 0
        }
      ]

      conditions = [{
        http_headers = [{
          http_header_name = "X-CF-Auth-Secret"
          values           = [random_string.random_cf_header_val_podenergy[0].result]
        }]
      }]
    }
  ] : [])

  https_listeners = [
    {
      port            = 443
      protocol        = "HTTPS"
      certificate_arn = var.podenergy_route53_record_name != null ? module.dual_domain_acm[0].arn : module.acm.arn
      action_type     = "fixed-response"

      fixed_response = {
        content_type = "text/plain"
        message_body = "Access denied"
        status_code  = "403"
      }
    }
  ]


  http_tcp_listeners = [
    {
      port        = 80
      protocol    = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ]

  security_group_name        = "${local.identifier}-lb"
  security_group_description = "Security Group for the ${local.identifier} load balancer."

  security_group_ingress_rules = {
    "port_http_all" = {
      description = "HTTP permitted from the Internet."
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }

    "port_https_all" = {
      description = "HTTPS permitted from the Internet."
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }
}

module "alb_access_logs_database" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws//modules/athena_access_logs_database"
  version = "4.1.1"
  tags    = local.tags

  athena_workgroup_identifier          = local.identifier
  athena_workgroup_description         = format("Collection of settings for %s", local.identifier)
  athena_database_identifier           = replace(local.identifier, "-", "_")
  glue_catalogue_identifier            = "alb_access_logs"
  s3_bucket_name                       = module.alb.s3_access_log_bucket_id
  query_execution_bucket_force_destroy = true
}
