output "api_security_group_id" {
  description = "The API security group ID"
  value       = module.api.security_group_id
}

output "events_topic_arn" {
  description = "The ARN of the topic to which OCPI events are published"
  value       = aws_sns_topic.ocpi_events.arn
}

output "queue_worker_security_group_id" {
  description = "The queue worker security group ID"
  value       = module.queue_worker.security_group_id
}

output "podenergy_cloudfront_distribution_id" {
  description = "The CloudFront distribution ID for the podenergy.com domain"
  value       = var.enable_podenergy_domain && var.podenergy_route53_record_name != null ? aws_cloudfront_distribution.podenergy[0].id : null
}

output "podenergy_cloudfront_domain_name" {
  description = "The CloudFront distribution domain name for the podenergy.com domain"
  value       = var.enable_podenergy_domain && var.podenergy_route53_record_name != null ? aws_cloudfront_distribution.podenergy[0].domain_name : null
}

output "dual_domain_acm_arn" {
  description = "The ARN of the dual-domain ACM certificate"
  value       = var.enable_podenergy_domain ? module.dual_domain_acm[0].arn : null
}

output "dual_domain_acm_us_east_1_arn" {
  description = "The ARN of the dual-domain ACM certificate in us-east-1"
  value       = var.enable_podenergy_domain ? module.dual_domain_acm_us_east_1[0].arn : null
}
