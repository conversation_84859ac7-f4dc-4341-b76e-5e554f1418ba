# Single domain certificates for pod-point.com only (legacy)
module "acm" {
  source = "../../../../common/acm"
  tags   = local.tags

  providers = {
    aws.acm     = aws,
    aws.route53 = aws.route53
  }

  domain_name                = local.pod_point_com_domain_name
  hosted_zone_id             = local.pod_point_com_hosted_zone
  additional_aliased_domains = [format("*.%s", local.pod_point_com_domain_name)]
}

module "acm_us_east_1" {
  source = "../../../../common/acm"
  tags   = local.tags

  providers = {
    aws.acm     = aws.us-east-1,
    aws.route53 = aws.route53
  }

  domain_name                = local.pod_point_com_domain_name
  hosted_zone_id             = local.pod_point_com_hosted_zone
  additional_aliased_domains = [format("*.%s", local.pod_point_com_domain_name)]
}

# Dual domain certificates supporting both pod-point.com and podenergy.com
locals {
  zone_mapping = {
    (local.pod_point_com_domain_name) = local.pod_point_com_hosted_zone,
    (local.podenergy_com_domain_name) = local.podenergy_com_hosted_zone,
  }
}

module "dual_domain_acm" {
  count = var.podenergy_route53_record_name != null ? 1 : 0

  source  = "terraform-aws-modules/acm/aws"
  version = "5.1.1"

  providers = {
    aws = aws,
  }

  domain_name = local.pod_point_com_domain_name
  subject_alternative_names = [
    format("*.%s", local.pod_point_com_domain_name),
    local.podenergy_com_domain_name,
    format("*.%s", local.podenergy_com_domain_name),
  ]

  create_route53_records  = false
  validation_method       = "DNS"
  validation_record_fqdns = module.dual_domain_acm_dns_validation[0].validation_route53_record_fqdns

  tags = local.tags
}

module "dual_domain_acm_dns_validation" {
  count = var.podenergy_route53_record_name != null ? 1 : 0

  source  = "terraform-aws-modules/acm/aws"
  version = "5.1.1"

  providers = {
    aws = aws.route53
  }

  create_certificate          = false
  create_route53_records_only = true
  validation_method           = "DNS"

  distinct_domain_names                     = module.dual_domain_acm[0].distinct_domain_names
  acm_certificate_domain_validation_options = module.dual_domain_acm[0].acm_certificate_domain_validation_options

  zone_id = local.pod_point_com_hosted_zone
  zones = {
    (local.pod_point_com_domain_name) = local.pod_point_com_hosted_zone,
    (local.podenergy_com_domain_name) = local.podenergy_com_hosted_zone,
  }

  tags = local.tags
}

module "dual_domain_acm_us_east_1" {
  count = var.podenergy_route53_record_name != null ? 1 : 0

  source  = "terraform-aws-modules/acm/aws"
  version = "5.1.1"

  providers = {
    aws = aws.us-east-1,
  }

  domain_name = local.pod_point_com_domain_name
  subject_alternative_names = [
    format("*.%s", local.pod_point_com_domain_name),
    local.podenergy_com_domain_name,
    format("*.%s", local.podenergy_com_domain_name),
  ]

  create_route53_records  = false
  validation_method       = "DNS"
  validation_record_fqdns = module.dual_domain_acm_us_east_1_dns_validation[0].validation_route53_record_fqdns

  tags = local.tags
}

module "dual_domain_acm_us_east_1_dns_validation" {
  count = var.podenergy_route53_record_name != null ? 1 : 0

  source  = "terraform-aws-modules/acm/aws"
  version = "5.1.1"

  providers = {
    aws = aws.route53
  }

  create_certificate          = false
  create_route53_records_only = true
  validation_method           = "DNS"

  distinct_domain_names                     = module.dual_domain_acm_us_east_1[0].distinct_domain_names
  acm_certificate_domain_validation_options = module.dual_domain_acm_us_east_1[0].acm_certificate_domain_validation_options

  zone_id = local.pod_point_com_hosted_zone
  zones = {
    (local.pod_point_com_domain_name) = local.pod_point_com_hosted_zone,
    (local.podenergy_com_domain_name) = local.podenergy_com_hosted_zone,
  }

  tags = local.tags
}
