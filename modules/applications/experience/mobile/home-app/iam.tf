// Notifications API
data "aws_iam_policy_document" "notifications_api_ecs_task_execution_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend_notifications_api.arn]
  }
  statement {
    sid     = "RetrieveSecretManagerSecretValues"
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.notifications_api.arn
    ]
  }
}

data "aws_iam_policy_document" "notifications_api_ecs_task_container_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend_notifications_api.arn]
  }
  statement {
    sid = "AllowSessionManager"
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
  }
  statement {
    sid = "AllowAccessToSqsQueues"
    actions = [
      "sqs:DeleteMessage",
      "sqs:ReceiveMessage",
      "sqs:SendMessage"
    ]
    resources = [
      aws_sqs_queue.notification_events.arn,
      aws_sqs_queue.enode_credential_intervention_events.arn,
      aws_sqs_queue.enode_vehicle_intervention_events.arn,
    ]
  }
  statement {
    sid = "AllowConnectionToRDS"
    actions = [
      "rds-db:connect"
    ]
    resources = [
      "arn:aws:rds-db:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:dbuser:${var.aurora_cluster_resource_id}/${var.aurora_cluster_user}",
    ]
  }
}

data "aws_iam_policy_document" "backend_notifications_api" {
  statement {
    sid       = "EnableAccountAdministration"
    actions   = ["kms:*"]
    resources = ["*"]

    principals {
      type        = "AWS"
      identifiers = concat([format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id)], var.account_software_engineer_iam_role_arn)
    }
  }

  statement {
    sid       = "AllowKMSDecrypt"
    actions   = ["kms:Decrypt"]
    resources = ["*"]

    principals {
      type = "AWS"
      identifiers = [
        module.notifications_api.execution_role_arn,
        module.notifications_api.task_role_arn
      ]
    }
  }

  statement {
    sid = "AllowKMSDecryptAdminBreakGlass"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    condition {
      test     = "StringLike"
      variable = "aws:PrincipalArn"
      values   = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"]
    }
  }
}

// Rewards API
data "aws_iam_policy_document" "rewards_api_ecs_task_execution_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend_rewards_api.arn]
  }
  statement {
    sid       = "RetrieveSecretManagerSecretValues"
    actions   = ["secretsmanager:GetSecretValue"]
    resources = [aws_secretsmanager_secret.rewards_api.arn]
  }
}

data "aws_iam_policy_document" "rewards_api_ecs_task_container_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend_rewards_api.arn]
  }
  statement {
    sid = "AllowSessionManager"
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
  }
  statement {
    sid = "AllowConnectionToRDS"
    actions = [
      "rds-db:connect"
    ]
    resources = [
      "arn:aws:rds-db:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:dbuser:${var.aurora_cluster_resource_id}/${var.aurora_cluster_user}",
    ]
  }
  statement {
    sid = "AllowAccessToSqsQueues"
    actions = [
      "sqs:DeleteMessage",
      "sqs:ReceiveMessage",
      "sqs:SendMessage"
    ]
    resources = [
      aws_sqs_queue.rewards_api_incoming_events.arn,
    ]
  }
}

data "aws_iam_policy_document" "backend_rewards_api" {
  statement {
    sid       = "EnableAccountAdministration"
    actions   = ["kms:*"]
    resources = ["*"]

    principals {
      type        = "AWS"
      identifiers = concat([format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id)], var.account_software_engineer_iam_role_arn)
    }
  }

  statement {
    sid       = "AllowKMSDecrypt"
    actions   = ["kms:Decrypt"]
    resources = ["*"]

    principals {
      type = "AWS"
      identifiers = [
        module.rewards_api.execution_role_arn,
        module.rewards_api.task_role_arn
      ]
    }
  }

  statement {
    sid = "AllowKMSDecryptAdminBreakGlass"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    condition {
      test     = "StringLike"
      variable = "aws:PrincipalArn"
      values   = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"]
    }
  }
}

# Subscriptions API
data "aws_iam_policy_document" "subscriptions_api_ecs_task_execution_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend_subscriptions_api.arn]
  }
  statement {
    sid       = "RetrieveSecretManagerSecretValues"
    actions   = ["secretsmanager:GetSecretValue"]
    resources = [aws_secretsmanager_secret.subscriptions_api.arn]
  }
}

data "aws_iam_policy_document" "subscriptions_api_ecs_task_container_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend_subscriptions_api.arn]
  }
  statement {
    sid = "AllowSessionManager"
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
  }
  statement {
    sid = "AllowConnectionToRDS"
    actions = [
      "rds-db:connect"
    ]
    resources = [
      "arn:aws:rds-db:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:dbuser:${var.aurora_cluster_resource_id}/${var.aurora_cluster_user}",
    ]
  }
  statement {
    sid = "AllowAccessToSqsQueues"
    actions = [
      "sqs:DeleteMessage",
      "sqs:ReceiveMessage",
      "sqs:SendMessage"
    ]
    resources = [
      aws_sqs_queue.subscriptions_api_incoming_events.arn,
    ]
  }
}

data "aws_iam_policy_document" "backend_subscriptions_api" {
  statement {
    sid       = "EnableAccountAdministration"
    actions   = ["kms:*"]
    resources = ["*"]

    principals {
      type        = "AWS"
      identifiers = concat([format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id)], var.account_software_engineer_iam_role_arn)
    }
  }

  statement {
    sid       = "AllowKMSDecrypt"
    actions   = ["kms:Decrypt"]
    resources = ["*"]

    principals {
      type = "AWS"
      identifiers = [
        module.subscriptions_api.execution_role_arn,
        module.subscriptions_api.task_role_arn
      ]
    }
  }

  statement {
    sid = "AllowKMSDecryptAdminBreakGlass"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    condition {
      test     = "StringLike"
      variable = "aws:PrincipalArn"
      values   = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"]
    }
  }
}


# Events
data "aws_iam_policy_document" "permit_notification_event" {
  statement {
    sid    = "PermitEventBridgeSendMessage"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }

    actions   = ["sqs:SendMessage"]
    resources = [aws_sqs_queue.notification_events.arn]

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"
      values   = [var.experience_notification_eventbridge_rule_arn]
    }
  }
}

data "aws_iam_policy_document" "permit_enode_credential_intervention_event" {
  statement {
    sid    = "PermitEventBridgeSendMessage"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }

    actions   = ["sqs:SendMessage"]
    resources = [aws_sqs_queue.enode_credential_intervention_events.arn]

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"
      values   = [var.experience_enode_credential_intervention_rule_arn]
    }
  }
}

data "aws_iam_policy_document" "permit_enode_vehicle_intervention_event" {
  statement {
    sid    = "PermitEventBridgeSendMessage"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }

    actions   = ["sqs:SendMessage"]
    resources = [aws_sqs_queue.enode_vehicle_intervention_events.arn]

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"
      values   = [var.experience_enode_vehicle_intervention_rule_arn]
    }
  }
}

data "aws_iam_policy_document" "permit_user_pool_event" {
  statement {
    sid    = "PermitEventBridgeSendMessage"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }

    actions   = ["sqs:SendMessage"]
    resources = [aws_sqs_queue.user_profile_events.arn]

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"
      values   = [var.experience_user_pool_rule_arn]
    }
  }
}

// Payments API
data "aws_iam_policy_document" "payments_api_ecs_task_execution_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend_payments_api.arn]
  }
  statement {
    sid       = "RetrieveSecretManagerSecretValues"
    actions   = ["secretsmanager:GetSecretValue"]
    resources = [aws_secretsmanager_secret.payments_api.arn]
  }
}

data "aws_iam_policy_document" "payments_api_ecs_task_container_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend_payments_api.arn]
  }
  statement {
    sid = "AllowSessionManager"
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
  }
  statement {
    sid = "AllowAccessToSqsQueues"
    actions = [
      "sqs:DeleteMessage",
      "sqs:ReceiveMessage",
      "sqs:SendMessage"
    ]
    resources = [
      aws_sqs_queue.payments_events.arn
    ]
  }
  statement {
    sid = "AllowConnectionToRDS"
    actions = [
      "rds-db:connect"
    ]
    resources = [
      "arn:aws:rds-db:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:dbuser:${var.aurora_cluster_resource_id}/${var.aurora_cluster_user}",
    ]
  }
}

data "aws_iam_policy_document" "backend_payments_api" {
  statement {
    sid       = "EnableAccountAdministration"
    actions   = ["kms:*"]
    resources = ["*"]

    principals {
      type        = "AWS"
      identifiers = concat([format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id)], var.account_software_engineer_iam_role_arn)
    }
  }

  statement {
    sid       = "AllowKMSDecrypt"
    actions   = ["kms:Decrypt"]
    resources = ["*"]

    principals {
      type = "AWS"
      identifiers = [
        module.payments_api.execution_role_arn,
        module.payments_api.task_role_arn
      ]
    }
  }

  statement {
    sid = "AllowKMSDecryptAdminBreakGlass"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    condition {
      test     = "StringLike"
      variable = "aws:PrincipalArn"
      values   = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"]
    }
  }
}

// Subscriptions Gateway
data "aws_iam_policy_document" "subscriptions_gateway_assume_role" {
  version = "2012-10-17"

  statement {
    sid     = "AssumeAPIGatewayRole"
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["apigateway.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "subscriptions_gateway" {
  assume_role_policy = data.aws_iam_policy_document.subscriptions_gateway_assume_role.json
}

data "aws_iam_policy_document" "subscriptions_gateway_policy" {
  version = "2012-10-17"

  statement {
    effect  = "Allow"
    actions = ["sqs:SendMessage"]
    resources = [
      aws_sqs_queue.subscriptions_api_incoming_events.arn
    ]
  }
}

resource "aws_iam_policy" "subscriptions_gateway" {
  name   = aws_api_gateway_rest_api.subscriptions_gateway.name
  policy = data.aws_iam_policy_document.subscriptions_gateway_policy.json
}

resource "aws_iam_role_policy_attachment" "subscriptions_gateway" {
  role       = aws_iam_role.subscriptions_gateway.name
  policy_arn = aws_iam_policy.subscriptions_gateway.arn
}
