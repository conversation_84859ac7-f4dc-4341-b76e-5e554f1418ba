locals {
  redis_port          = 6379
  cache_members_blue  = tolist(module.cache_blue.member_clusters)
  cache_members_green = tolist(one(module.cache_green[*].member_clusters))
}

moved {
  from = module.cache
  to   = module.cache_blue
}

module "cache_blue" {
  source  = "terraform-enterprise.pod-point.com/technology/elasticache-redis/aws"
  version = "1.1.0"

  identifier  = local.identifier
  description = format("Elasticache Redis cluster for %s", local.identifier)
  port        = local.redis_port
  vpc_id      = var.vpc_id
  subnet_ids  = var.ecs_private_subnets_ids

  multi_az_enabled = var.environment != "dev"

  instance_type  = var.cache_instance_type
  engine_version = "7.0"

  cache_parameter_group_family = "redis7"

  cloudwatch_metric_alarms_enabled = true
  alarm_actions                    = [data.aws_sns_topic.opsgenie.arn]
  alarm_ok_actions                 = [data.aws_sns_topic.opsgenie.arn]

  cluster_mode_enabled                 = true
  cluster_mode_replicas_per_node_group = var.cache_replicas_per_node_group
  cluster_mode_num_node_groups         = var.cache_node_group_count

  at_rest_encryption_kms_key_arn                   = module.cache_kms.arn
  secret_manager_auth_token_kms_encryption_key_arn = module.cache_kms.arn

  tags = var.tags
}

module "cache_green" {
  count = local.blue_green_enabled ? 1 : 0

  source  = "terraform-enterprise.pod-point.com/technology/elasticache-redis/aws"
  version = "1.1.0"

  identifier  = local.identifier_green
  description = format("Elasticache Redis cluster for %s", local.identifier_green)
  port        = local.redis_port
  vpc_id      = var.vpc_id
  subnet_ids  = var.ecs_private_subnets_ids

  multi_az_enabled = var.environment != "dev"

  instance_type  = var.cache_instance_type
  engine_version = "7.0"

  cache_parameter_group_family = "redis7"

  cloudwatch_metric_alarms_enabled = true
  alarm_actions                    = [data.aws_sns_topic.opsgenie.arn]
  alarm_ok_actions                 = [data.aws_sns_topic.opsgenie.arn]

  cluster_mode_enabled                 = true
  cluster_mode_replicas_per_node_group = var.cache_replicas_per_node_group
  cluster_mode_num_node_groups         = var.cache_node_group_count

  at_rest_encryption_kms_key_arn                   = module.cache_kms.arn
  secret_manager_auth_token_kms_encryption_key_arn = module.cache_kms.arn

  tags = var.tags
}

moved {
  from = aws_cloudwatch_metric_alarm.cache_capacity_usage
  to   = aws_cloudwatch_metric_alarm.cache_capacity_usage_blue
}

resource "aws_cloudwatch_metric_alarm" "cache_capacity_usage_blue" {
  count               = var.cache_replicas_per_node_group * (var.cache_node_group_count + 1)
  alarm_name          = "${element(local.cache_members_blue, count.index)}-capacity-usage-blue"
  alarm_description   = "Redis cluster capacity usage (blue)"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "DatabaseCapacityUsagePercentage"
  namespace           = "AWS/ElastiCache"
  period              = "60"
  statistic           = "Average"

  threshold = "75"

  dimensions = {
    CacheClusterId = element(local.cache_members_blue, count.index)
  }

  alarm_actions = [data.aws_sns_topic.opsgenie.arn]
  ok_actions    = [data.aws_sns_topic.opsgenie.arn]

  tags = var.tags
}

moved {
  from = aws_cloudwatch_metric_alarm.cache_memory_usage
  to   = aws_cloudwatch_metric_alarm.cache_memory_usage_blue
}

resource "aws_cloudwatch_metric_alarm" "cache_memory_usage_blue" {
  count               = var.cache_replicas_per_node_group * (var.cache_node_group_count + 1)
  alarm_name          = "${element(local.cache_members_blue, count.index)}-memory-usage-blue"
  alarm_description   = "Redis cluster memory usage (blue)"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "DatabaseMemoryUsagePercentage"
  namespace           = "AWS/ElastiCache"
  period              = "60"
  statistic           = "Average"

  threshold = "75"

  dimensions = {
    CacheClusterId = element(local.cache_members_blue, count.index)
  }

  alarm_actions = [data.aws_sns_topic.opsgenie.arn]
  ok_actions    = [data.aws_sns_topic.opsgenie.arn]

  tags = var.tags
}

resource "aws_cloudwatch_metric_alarm" "cache_capacity_usage_green" {
  count               = local.blue_green_enabled ? var.cache_replicas_per_node_group * (var.cache_node_group_count + 1) : 0
  alarm_name          = "${element(local.cache_members_green, count.index)}-capacity-usage-green"
  alarm_description   = "Redis cluster capacity usage (green)"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "DatabaseCapacityUsagePercentage"
  namespace           = "AWS/ElastiCache"
  period              = "60"
  statistic           = "Average"

  threshold = "75"

  dimensions = {
    CacheClusterId = element(local.cache_members_green, count.index)
  }

  alarm_actions = [data.aws_sns_topic.opsgenie.arn]
  ok_actions    = [data.aws_sns_topic.opsgenie.arn]

  tags = var.tags
}

resource "aws_cloudwatch_metric_alarm" "cache_memory_usage_green" {
  count               = local.blue_green_enabled ? var.cache_replicas_per_node_group * (var.cache_node_group_count + 1) : 0
  alarm_name          = "${element(local.cache_members_green, count.index)}-memory-usage-green"
  alarm_description   = "Redis cluster memory usage (green)"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "DatabaseMemoryUsagePercentage"
  namespace           = "AWS/ElastiCache"
  period              = "60"
  statistic           = "Average"

  threshold = "75"

  dimensions = {
    CacheClusterId = element(local.cache_members_green, count.index)
  }

  alarm_actions = [data.aws_sns_topic.opsgenie.arn]
  ok_actions    = [data.aws_sns_topic.opsgenie.arn]

  tags = var.tags
}

module "cache_kms" {
  source                   = "terraform-enterprise.pod-point.com/technology/kms/aws"
  version                  = "1.1.0"
  tags                     = var.tags
  alias_name               = format("%s-cache", local.identifier)
  description              = "Used to encrypt the website cms cache."
  policy                   = data.aws_iam_policy_document.aurora_kms.json
  enable_aws_backup_access = var.environment == "prod"
}

data "aws_iam_policy_document" "diagnostics_kms_policy" {
  statement {
    sid     = "Enable Account Access"
    actions = ["kms:*"]
    resources = [
      "*"
    ]
    principals {
      type        = "AWS"
      identifiers = [format("arn:aws:iam::%s:root", data.aws_caller_identity.current.account_id)]
    }
  }
}

resource "aws_security_group_rule" "web_service_ingress_redis" {
  description              = "Permit Redis ingress traffic for the Pod Point website."
  type                     = "ingress"
  from_port                = local.redis_port
  to_port                  = local.redis_port
  protocol                 = "tcp"
  source_security_group_id = module.web_service.security_group_id
  security_group_id        = module.cache_blue.security_group_id
}

resource "aws_security_group_rule" "cli_migration_ingress_redis" {
  description              = "Permit Redis ingress traffic for the Pod Point website database migrations."
  type                     = "ingress"
  from_port                = local.redis_port
  to_port                  = local.redis_port
  protocol                 = "tcp"
  source_security_group_id = module.cli_migration.security_group_id
  security_group_id        = module.cache_blue.security_group_id
}

resource "aws_security_group_rule" "listener_service_ingress_redis" {
  description              = "Permit Redis ingress traffic for the Pod Point queue listener."
  type                     = "ingress"
  from_port                = local.redis_port
  to_port                  = local.redis_port
  protocol                 = "tcp"
  source_security_group_id = module.listener_service.security_group_id
  security_group_id        = module.cache_blue.security_group_id
}

resource "aws_security_group_rule" "vpn_ingress_redis" {
  description       = "Permit Redis ingress traffic via the VPN"
  type              = "ingress"
  from_port         = local.redis_port
  to_port           = local.redis_port
  protocol          = "tcp"
  cidr_blocks       = [var.vpn_cidr]
  security_group_id = module.cache_blue.security_group_id
}

resource "aws_security_group_rule" "web_service_ingress_redis_green" {
  count                    = local.blue_green_enabled ? 1 : 0
  description              = "Permit Redis ingress traffic for the Pod Point website (green)."
  type                     = "ingress"
  from_port                = local.redis_port
  to_port                  = local.redis_port
  protocol                 = "tcp"
  source_security_group_id = one(module.web_service_green[*].security_group_id)
  security_group_id        = one(module.cache_green[*].security_group_id)
}

resource "aws_security_group_rule" "cli_migration_ingress_redis_green" {
  count                    = local.blue_green_enabled ? 1 : 0
  description              = "Permit Redis ingress traffic for the Pod Point website database migrations (green)."
  type                     = "ingress"
  from_port                = local.redis_port
  to_port                  = local.redis_port
  protocol                 = "tcp"
  source_security_group_id = one(module.cli_migration_green[*].security_group_id)
  security_group_id        = one(module.cache_green[*].security_group_id)
}

resource "aws_security_group_rule" "listener_service_ingress_redis_green" {
  count                    = local.blue_green_enabled ? 1 : 0
  description              = "Permit Redis ingress traffic for the Pod Point queue listener (green)."
  type                     = "ingress"
  from_port                = local.redis_port
  to_port                  = local.redis_port
  protocol                 = "tcp"
  source_security_group_id = one(module.listener_service_green[*].security_group_id)
  security_group_id        = one(module.cache_green[*].security_group_id)
}

resource "aws_security_group_rule" "vpn_ingress_redis_green" {
  count             = local.blue_green_enabled ? 1 : 0
  description       = "Permit Redis ingress traffic via the VPN (green)"
  type              = "ingress"
  from_port         = local.redis_port
  to_port           = local.redis_port
  protocol          = "tcp"
  cidr_blocks       = [var.vpn_cidr]
  security_group_id = one(module.cache_green[*].security_group_id)
}
