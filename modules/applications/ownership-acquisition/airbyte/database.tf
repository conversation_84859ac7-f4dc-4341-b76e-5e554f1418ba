// To be used instead of the data resource for db password in future PR
resource "random_password" "this" {
  length           = 16
  special          = true
  override_special = "#&()"
}

resource "aws_secretsmanager_secret" "db_admin_credentials" {
  tags = var.tags

  name        = "/rds/${var.database_identifier}/admin/credentials"
  description = "Airbyte RDS admin user credentials."
}

resource "aws_secretsmanager_secret_version" "db_admin_credentials" {
  secret_id = aws_secretsmanager_secret.db_admin_credentials.id
  secret_string = jsonencode({
    user     = var.database_admin_username
    password = random_password.this.result
  })
}

resource "aws_db_instance" "airbyte" {
  tags = var.tags

  identifier                = var.database_identifier
  allocated_storage         = var.database_allocated_storage
  max_allocated_storage     = var.database_max_allocated_storage
  engine                    = "postgres"
  engine_version            = var.database_engine_version
  backup_retention_period   = 2
  multi_az                  = var.database_multi_az
  backup_window             = var.backup_window
  maintenance_window        = var.maintenance_window
  instance_class            = var.database_instance_type
  port                      = var.database_port
  db_subnet_group_name      = resource.aws_db_subnet_group.private.id
  vpc_security_group_ids    = [aws_security_group.airbyte_db.id]
  skip_final_snapshot       = var.database_skip_final_snapshot
  final_snapshot_identifier = var.database_skip_final_snapshot ? null : var.database_identifier
  publicly_accessible       = false
  storage_encrypted         = true // AWS managed KMS is created
  db_name                   = var.db_name
  username                  = var.database_admin_username
  password                  = jsondecode(resource.aws_secretsmanager_secret_version.db_admin_credentials.secret_string)["password"]
}

resource "aws_db_subnet_group" "airbyte_db" {
  tags = var.tags

  name       = var.database_identifier
  subnet_ids = var.private_subnet_ids
}

resource "aws_security_group" "airbyte_db" {
  tags = var.tags

  name        = "airbyte-postgres-sg-${var.environment}"
  vpc_id      = var.vpc_id
  description = "Security group for RDS instance containing logs for airbyte."

  ingress {
    description = "Allow internal connection from airbyte instance."
    from_port   = var.database_port
    to_port     = var.database_port
    protocol    = "tcp"
    security_groups = [
      aws_security_group.airbyte_server.id
    ]
  }

  ingress {
    description = "Accessible via vpn."
    from_port   = var.database_port
    to_port     = var.database_port
    protocol    = "tcp"
    cidr_blocks = [
      var.vpn_cidr
    ]
  }

  egress {
    protocol         = "-1"
    from_port        = 0
    to_port          = 0
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}

// Aurora database config

module "aurora" {
  source  = "terraform-enterprise.pod-point.com/technology/aurora/aws"
  version = "5.2.2"

  identifier = "airbyte"
  vpc_id     = var.vpc_id

  subnet_group_ids = var.private_subnet_ids

  engine         = "aurora-postgresql"
  engine_version = "14.15"

  port                      = var.database_port
  admin_username            = var.database_admin_username
  database_name             = var.db_name
  aurora_managed_admin_user = false

  storage_kms_encryption_key           = module.kms.arn
  cluster_preffered_maintenance_window = "Mon:02:00-Mon:03:00"
  cluster_preffered_backup_window      = "00:30-02:00"
  enable_aws_backup                    = var.environment == "prod" ? true : false
  cluster_deletion_protection          = true
  cluster_allow_major_version_upgrade  = true

  # Parameters
  cluster_parameter_group_family              = "aurora-postgresql14"
  cluster_parameters                          = {}
  cluster_iam_database_authentication_enabled = true
  apply_cluster_changes_immediately           = true

  ca_cert_identifier = "rds-ca-rsa2048-g1"

  instance_enable_performance_insights = true
  instance_parameter_group_family      = "aurora-postgresql14"
  instance_parameters                  = {}

  # Cluster instances
  cluster_instance_class = var.aurora_cluster_instance_class
  cluster_instance_count = var.aurora_cluster_instance_count

  tags = var.tags
}

resource "aws_security_group_rule" "aurora_cluster_egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  description       = "Permit all egress traffic."
  security_group_id = module.aurora.security_group_id
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
}

resource "aws_security_group_rule" "aurora_cluster_airbyte_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  description              = "Access permitted from airbyte."
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = aws_security_group.airbyte_server.id
}

resource "aws_security_group_rule" "aurora_cluster_vpn_ingress" {
  type              = "ingress"
  from_port         = module.aurora.port
  to_port           = module.aurora.port
  protocol          = "TCP"
  description       = "Access permitted from vpn."
  security_group_id = module.aurora.security_group_id
  cidr_blocks = [
    var.vpn_cidr
  ]
}

data "aws_secretsmanager_secret" "aurora" {
  arn = module.aurora.admin_user_secret_manager_arn
}

data "aws_secretsmanager_secret_version" "aurora" {
  secret_id = data.aws_secretsmanager_secret.aurora.id
}
