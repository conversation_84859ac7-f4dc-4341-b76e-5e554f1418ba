resource "aws_cloudwatch_dashboard" "main" {
  dashboard_name = local.identifier
  dashboard_body = jsonencode({
    "widgets" : [
      {
        "height" : 6,
        "width" : 8,
        "y" : 0,
        "x" : 0,
        "type" : "metric",
        "properties" : {
          "metrics" : [
            [
              "AWS/ApiGateway", "Count", "ApiName", aws_api_gateway_rest_api.this.name,
              "Resource", "/referrals/bmw", "Stage", var.environment, "Method", "POST",
              { region : var.region }
            ],
            [
              ".", "4XXError", ".", ".", ".", ".", ".", ".", ".", ".",
              { yAxis : "right", color : "#ff7f0e" }
            ],
            [
              ".", "5XXError", ".", ".", ".", ".", ".", ".", ".", ".",
              { yAxis : "right", color : "#d62728" }
            ]
          ],
          "view" : "timeSeries",
          "stacked" : false,
          "region" : var.region,
          "title" : "BMW Referrals Request Count",
          "stat" : "Sum",
          "period" : 300
        }
      },
      {
        "height" : 6,
        "width" : 8,
        "y" : 6,
        "x" : 16,
        "type" : "metric",
        "properties" : {
          "metrics" : [
            [
              "AWS/ApiGateway", "Latency", "ApiName", aws_api_gateway_rest_api.this.name,
              "Resource", "/referrals/bmw", "Stage", var.environment, "Method", "POST",
              { region : var.region }
            ],
            [
              ".", "IntegrationLatency", ".", ".", ".", ".", ".", ".", ".", ".",
              { region : var.region }
            ]
          ],
          "view" : "timeSeries",
          "stacked" : false,
          "region" : var.region,
          "stat" : "p99",
          "period" : 300,
          "title" : "BMW Referrals Latency"
        }
      },
      {
        "type" : "log",
        "x" : 0,
        "y" : 12,
        "width" : 24,
        "height" : 6,
        "properties" : {
          "query" : "SOURCE '${aws_cloudwatch_log_group.bmw_event_logging.name}' | fields @timestamp, @message as payload\n| sort @timestamp desc\n| limit 50",
          "region" : var.region,
          "stacked" : false,
          "view" : "table",
          "title" : "Recent events"
        }
      }
    ]
  })
}
