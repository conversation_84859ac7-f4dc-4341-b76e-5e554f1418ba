variable "namespace_name" {
  description = "VPC ID where the private namespace and services will be deployed to."
  type        = string
  default     = null
}

variable "namespace_description" {
  description = "VPC ID where the private namespace and services will be deployed to."
  type        = string
  default     = null
}

variable "services" {
  description = "A list of objects describing the services attached to the namespace."
  type = list(object({
    name           = string
    description    = optional(string)
    dns_ttl        = optional(number) # The amount of time, in seconds, that you want DNS resolvers to cache the settings for this resource record set. Defaults to 0.
    dns_type       = optional(string) # The type of the resource, which indicates the value that Amazon Route 53 returns in response to DNS queries. Valid Values: A, AAAA, SRV, CNAME. Defaults to A.
    routing_policy = optional(string) # The routing policy that you want to apply to all records that Route 53 creates when you register an instance and specify the service. Valid Values: MULTIVALUE, WEIGHTED.
  }))
  default = []
}

variable "vpc_id" {
  description = "VPC ID where the private namespace and services will be deployed to."
  type        = string
  default     = null
}