<p align="center">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://www.datocms-assets.com/2885/1620155114-brandhcterraformprimaryattributedcolorwhite.svg"
    >
    <img width="50%" height="50%" alt="Terraform" src="https://www.datocms-assets.com/2885/1620155113-brandhcterraformprimaryattributedcolor.svg">
</picture>
</p>

---

**Table of Contents**
- [Contributing 🧑‍💻](#contributing-)
- [Getting started](#getting-started)
  - [Commit signing ✍️](#commit-signing-️)
  - [Terraform enterprise](#terraform-enterprise)
    - [Requesting a new workspace](#requesting-a-new-workspace)
    - [Version Control System (VCS) 🚀](#version-control-system-vcs-)
    - [Setting up notifications](#setting-up-notifications)
  - [Development workflow](#development-workflow)
    - [Branching](#branching)
  - [CODEOWNERS](#codeowners)
- [Directory layout](#directory-layout)
- [Modules](#modules)
  - [Managed in the mono repo.](#managed-in-the-mono-repo)
  - [Managed outside the mono repo.](#managed-outside-the-mono-repo)
    - [Terraform enterprise private registry for modules](#terraform-enterprise-private-registry-for-modules)
  - [Requesting a new module](#requesting-a-new-module)
- [Recommendations and tips for your Terraform projects](#recommendations-and-tips-for-your-terraform-projects)
  - [Adding a README to your workspace directory](#adding-a-readme-to-your-workspace-directory)
  - [Designing your networking components](#designing-your-networking-components)
- [Further reading material](#further-reading-material)

---

Welcome to the `terraform` mono repository. 👋

This space is

- Used by the Cloud Platform squad to create and maintain small and lightweight modules that we standardise and encourage other squads to use.

- Used by `Pod Point` squads to create and maintain infrastructure they declare as code.

- Used to encourage best practises through `code ownership`, `approval stages`, and implementing standardised boilerplates (Cloud Platform built templates that can deploy a specific `thing`)

---

## Contributing 🧑‍💻

Before contributing to our repository. Please check out our [contributors](https://github.com/Pod-Point/terraform/tree/main/CONTRIBUTORS.md) documentation space.

## Getting started

The following sections will help onboard new employees with the guidelines and restrictions we have in place.

### Commit signing ✍️

_"Using GPG, SSH, or S/MIME, you can sign tags and commits locally. These tags or commits are marked as verified on GitHub so other people can be confident that the changes come from a trusted source."_ - Source: [GitHub - About Commit Signature Verification](https://docs.github.com/en/authentication/managing-commit-signature-verification/about-commit-signature-verification)

We want to encourage commit signing behaviour. To set this up, we recommend you follow the [Generate a new GPG key](https://docs.github.com/en/authentication/managing-commit-signature-verification/generating-a-new-gpg-key) guide.

When you have it setup correctly; You should start to see a status of `verified` next to your commits.

### Terraform enterprise

We use a self hosted terraform enterprise server to administer our terraform projects within this repository.

You can read more about getting access and using this service by reading this [runbook](https://podpoint.atlassian.net/wiki/spaces/SKB/pages/3612147854/Terraform+Enterprise) guide.

#### Requesting a new workspace

To request a new [workspace](https://www.terraform.io/cloud-docs/workspaces). Reach out to the Cloud Platform squad via the [`#squad-cloud-platform-support`](https://technology-pod-point.slack.com/archives/C04DD8EUMLY) Slack channel.

They will create the workspace, assign the relevant squad access to administer it, and help setup version control.

#### Version Control System (VCS) 🚀

A version control integration is created by the organisation admin. Then, for each workspace, additional settings like source repository url and a targeted branch can be provided to enable a CICD style workflow (depending on the settings).

We encourage each workspace to be configured with the following settings

- A manual apply method.
- enable automatic speculative plans.
- Scope the run trigger to the path of your project.

A full explanation for these settings can be found here: [workspace vcs settings](https://www.terraform.io/cloud-docs/workspaces/settings/vcs)

#### Setting up notifications

Squads / Domains that want to be alerted for the different stages of a terraform run within their workspaces can create an integration to Slack.

If you would like to do this, please reach out and we can look into this for you (we can supply a webhook that you can use). We'll need a slack channel to send your alerts to, and the workspaces you would like this to be set up on.

For further information around notifications. Please refer to the terraform docs at [Notifications](https://developer.hashicorp.com/terraform/cloud-docs/workspaces/settings/notifications)

### Development workflow

While developing changes for a given workload or module, it is expected that the following process is followed:

1. Make your changes locally. Optionally these changes can be tested by running `terraform plan` which will upload your local workspace to the Terraform Enterprise server and run the plan remotely. A link to the plan will be output on the console.

2. Push changes to a new branch and create a PR. This will trigger a new plan in your Terraform Enterprise workspace.

3. When you are satisfied that your plan contains the desired changes, and the PR has been reviewed, merge your PR to the main branch and apply the plan.

#### Branching

Plans can be created from feature branches, but should only ever be applied from the main branch. While it is possible to configure your Terraform Enterprise workspace to apply a plan from a branch, this is discouraged as it can lead to problems.

### CODEOWNERS

Alongside the creation of new Terraform workspace, we will update the [CODEOWNERS](.github/CODEOWNERS) file so that the right GitHub team will have the ability to own their own code.

This also means that the code owners mapped to a directory path will be able to approve and merge in changes in pull requests relating to those paths. (currently enforced only for the `main` branch).

## Directory layout

```
root_dir
│   README.md
|   .*
│
└───modules
|     |
|     └───applications
|     |   |
|     |   └ <workload>              Module containing building blocks for a whole workload.
|     |       |                     Setup so that it can be used by multiple environments
|     |       └───  "*.tf"
|     |
|     └───common                    Lightweight modules for highly used infrastructure.
|     |
|     └---<other>                   A module that doesn't belong in any other folder within
|                                   this space.
|
└───workload                        Contains workloads and is the root source
       |                            location for many workloads.
       └─── <workload>-dev
       └─── <workload>-staging
       └─── <workload>-prod
       └─── ...
```

## Modules

We currently have different use-cases for modules.

### Managed in the mono repo.

Lightweight modules which the cloud platform squad manage directly within this mono repo.

These can be found in the `modules/` path.

- `modules/common` Contain the boilerplates for simple to use infrastructure. For example, the vpc module can be used to provision the required vpc, subnets, route tables following a pattern governed by the cloud platform squad.

- `modules/standardised` Contains more standardised approaches. For example, the patch manager module provisions a patch baselines, a maintenance window, and an intended target. This is enough to provide compliance for on-going patching of critical issues.

### Managed outside the mono repo.

We have a number of modules that we maintain and we cover outside this repository. They are managed in their own repository due to the size and the different number of approaches developers can take to use them. We have configured these modules to make use of version control, so that we can release updates and gradually update all upstream projects.

These modules are prefixed with the following github repository name `terraform-aws`
Here are a small commonly used modules.
- [terraform-aws-fargate-service](https://github.com/Pod-Point/terraform-aws-fargate-service)
- [terraform-aws-aurora](https://github.com/Pod-Point/terraform-aws-aurora)
- [terraform-aws-rds](https://github.com/Pod-Point/terraform-aws-rds)
- [terraform-aws-elasticache-redis](https://github.com/Pod-Point/terraform-aws-elasticache-redis)

#### Terraform enterprise private registry for modules

You will find that these types of modules are also published in our [private registry](https://terraform-enterprise.pod-point.com/app/pod-point/registry/private/modules).

We highly recommend you refer to the private registry for referencing documentation, examples, required inputs, outputs and any other useful information relating to that module.

**NOTE:** The registry strips out the `terraform-aws` prefix so the naming is showcased slightly different.

### Requesting a new module

If your project requires a module that we don't currently support. Please raise a ticket in the [Cloud Platform](https://podpoint.atlassian.net/jira/software/c/projects/DVO/boards/210) Jira board

## Recommendations and tips for your Terraform projects

### Adding a README to your workspace directory

You can provide additional context and help onboard people to your project by providing a README.md to your workspace
directory.

Terraform enterprise will automatically pick up the README and display it's contents on the workspace.

When writing up the README, Try to answer the following questions.

1. What is this workspace for? What goals are we hoping to achieve.
2. Can engineers new to this workspace make sense of the directory? If not, can I provide additional resources and links to other documentation.
3. Could I create a diagram to show what I've done. ([DrawIO](https://drawio-app.com/use-draw-io-offline/) is pretty good for creating infrastructure diagrams)

### Designing your networking components

Most applications need to reside within a VPC. To help you architect and choose a unique VPC CIDR, please have a read through of our confluence wiki: [VPC CIDR Range Governance](https://podpoint.atlassian.net/wiki/x/KQCetg)

## Further reading material

**Getting started with Terraform**
- [Terraform AWS Tutorials](https://learn.hashicorp.com/collections/terraform/aws-get-started)

- [Scratchpad](https://github.com/Pod-Point/terraform-scratchpad)
---

**Other**
- [What is Terraform Enterprise](https://www.terraform.io/enterprise)

- [Terraform Workspaces](https://www.terraform.io/cloud-docs/workspace)

- [Run Tasks](https://www.terraform.io/cloud-docs/integrations/run-tasks)

- [Team API tokens](https://www.terraform.io/enterprise/users-teams-organizations/api-tokens)

---

<img src="https://d3h256n3bzippp.cloudfront.net/pod-point-logo.svg" align="right" />

Travel shouldn't damage the earth 🌍

Made with ❤️&nbsp;&nbsp;at [Pod Point](https://pod-point.com)
